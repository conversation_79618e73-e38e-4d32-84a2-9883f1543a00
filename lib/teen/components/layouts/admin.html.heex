<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <!-- 品牌标识区域 -->
    <div class="flex items-center gap-3">
      <Backpex.HTML.Layout.topbar_branding />
      <!-- 系统标题 -->
      <div class="hidden lg:flex flex-col">
        <span class="text-sm font-semibold text-base-content">Teen Patti 管理系统</span>
        <span class="text-xs text-base-content/60">Admin Dashboard</span>
      </div>
    </div>
    
<!-- 中间工具栏区域 -->
    <div class="flex-1 flex items-center justify-center gap-4">
      <!-- 快速搜索框 -->
      <div class="hidden xl:flex items-center max-w-md w-full">
        <div class="relative w-full">
          <input
            type="text"
            placeholder="快速搜索用户、订单..."
            class="input input-sm input-bordered w-full pl-10 pr-12 bg-base-100/50 backdrop-blur-sm border-base-300/50 focus:border-primary/50 focus:bg-base-100 transition-all duration-200"
          />
          <.icon
            name="hero-magnifying-glass"
            class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60"
          />
          <kbd class="kbd kbd-xs absolute right-2 top-1/2 transform -translate-y-1/2 text-xs">
            ⌘K
          </kbd>
        </div>
      </div>
      
<!-- 系统状态指示器 -->
      <div class="hidden lg:flex items-center gap-3">
        <div class="tooltip tooltip-bottom" data-tip="在线用户">
          <div class="flex items-center gap-1 px-2 py-1 bg-success/10 rounded-full">
            <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span class="text-xs font-medium text-success">1,234</span>
          </div>
        </div>
        <div class="tooltip tooltip-bottom" data-tip="今日收入">
          <div class="flex items-center gap-1 px-2 py-1 bg-warning/10 rounded-full">
            <.icon name="hero-currency-dollar" class="size-3 text-warning" />
            <span class="text-xs font-medium text-warning">¥12.3K</span>
          </div>
        </div>
      </div>
    </div>
    
<!-- 右侧工具区域 -->
    <div class="flex items-center gap-2">
      <!-- 通知按钮 -->
      <div class="dropdown dropdown-end">
        <label
          tabindex="0"
          class="btn btn-ghost btn-sm btn-circle relative hover:bg-base-200/50 transition-colors"
        >
          <.icon name="hero-bell" class="size-5" />
          <!-- 通知徽章 -->
          <div class="absolute -top-1 -right-1 bg-error text-error-content rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold animate-pulse">
            3
          </div>
        </label>
        <div
          tabindex="0"
          class="dropdown-content z-[1] menu p-0 shadow-xl bg-base-100 rounded-box w-80 border border-base-300/50 backdrop-blur-sm"
        >
          <div class="p-4 border-b border-base-300/50 bg-gradient-to-r from-primary/5 to-secondary/5">
            <div class="flex items-center justify-between">
              <h3 class="font-semibold text-base-content">通知中心</h3>
              <div class="badge badge-primary badge-sm">3 新</div>
            </div>
          </div>
          <div class="max-h-64 overflow-y-auto">
            <div class="p-3 hover:bg-base-200/50 cursor-pointer border-b border-base-300/30 transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                    <.icon name="hero-user-plus" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">新用户注册</p>
                  <p class="text-xs text-base-content/60">用户 user123 刚刚注册</p>
                  <p class="text-xs text-base-content/40 mt-1">2分钟前</p>
                </div>
                <div class="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200/50 cursor-pointer border-b border-base-300/30 transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-warning text-warning-content rounded-full w-8 h-8">
                    <.icon name="hero-exclamation-triangle" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">异常充值订单</p>
                  <p class="text-xs text-base-content/60">订单 #12345 需要人工审核</p>
                  <p class="text-xs text-base-content/40 mt-1">5分钟前</p>
                </div>
                <div class="w-2 h-2 bg-warning rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200/50 cursor-pointer transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-error text-error-content rounded-full w-8 h-8">
                    <.icon name="hero-cpu-chip" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">系统警告</p>
                  <p class="text-xs text-base-content/60">服务器CPU使用率过高</p>
                  <p class="text-xs text-base-content/40 mt-1">10分钟前</p>
                </div>
                <div class="w-2 h-2 bg-error rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
          </div>
          <div class="p-3 border-t border-base-300/50 bg-base-50">
            <div class="flex gap-2">
              <button class="btn btn-sm btn-ghost flex-1">全部已读</button>
              <button class="btn btn-sm btn-primary flex-1">查看全部</button>
            </div>
          </div>
        </div>
      </div>
      
<!-- 增强主题选择器 -->
      <Backpex.HTML.Layout.theme_selector
        socket={@socket}
        class="enhanced-theme-selector"
        label="主题"
        themes={CypridinaWeb.ThemeConfig.format_for_backpex_selector()}
      />
      
<!-- 用户信息区域 -->
      <div class="flex items-center gap-3 ml-2">
        
<!-- 用户头像和下拉菜单 -->
        <Backpex.HTML.Layout.topbar_dropdown class="dropdown-end">
          <:label>
            <label
              tabindex="0"
              class="btn btn-ghost flex items-center gap-2 px-3 hover:bg-base-200/50 transition-colors"
            >
              <!-- 用户头像 -->
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-9 h-9 ring-2 ring-primary/20">
                  <span class="text-sm font-bold">
                    <%= if assigns[:current_user] && @current_user.username do %>
                      {String.first(to_string(@current_user.username))}
                    <% else %>
                      U
                    <% end %>
                  </span>
                </div>
              </div>
              <!-- 移动端显示用户名 -->
              <div class="lg:hidden flex flex-col items-start">
                <span class="text-sm font-medium">
                  <%= if assigns[:current_user] && @current_user.username do %>
                    {@current_user.username}
                  <% else %>
                    未知用户
                  <% end %>
                </span>
                <span class="text-xs opacity-60">
                  <%= if assigns[:current_user] && @current_user.role_name do %>
                    {@current_user.role_name}
                  <% else %>
                    普通用户
                  <% end %>
                </span>
              </div>
              <.icon name="hero-chevron-down" class="size-4 transition-transform duration-200" />
            </label>
          </:label>
          <!-- 用户菜单 -->
          <li>
            <.link
              navigate={~p"/admin/profile"}
              class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors"
            >
              <.icon name="hero-user-circle" class="size-5 text-primary" />
              <div class="flex flex-col">
                <span class="font-medium">个人信息</span>
                <span class="text-xs text-base-content/60">查看和编辑个人资料</span>
              </div>
            </.link>
          </li>
          <li>
            <.link
              navigate={~p"/admin/system-config"}
              class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors"
            >
              <.icon name="hero-cog-6-tooth" class="size-5 text-info" />
              <div class="flex flex-col">
                <span class="font-medium">系统设置</span>
                <span class="text-xs text-base-content/60">配置系统参数</span>
              </div>
            </.link>
          </li>
          <!-- 分隔线 -->
          <li><hr class="my-2 border-base-300" /></li>
          <!-- 退出登录 -->
          <li>
            <.link
              navigate={~p"/sign-out"}
              class="flex items-center gap-3 px-4 py-3 text-error hover:bg-error/10 transition-colors"
            >
              <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
              <div class="flex flex-col">
                <span class="font-medium">退出登录</span>
                <span class="text-xs text-error/60">安全退出系统</span>
              </div>
            </.link>
          </li>
        </Backpex.HTML.Layout.topbar_dropdown>
      </div>
    </div>
  </:topbar>
  <:sidebar>
    <!-- 用户管理 -->
    <.sidebar_section id="user-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-users" class="size-4 text-primary" />
          <span class="font-semibold">用户管理</span>
        </div>
      </:label>
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/users"}
        icon="hero-user"
        text="用户列表"
        color="primary"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-management"}
        icon="hero-users"
        text="用户管理"
        color="primary"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/roles"}
        icon="hero-users"
        text="权限组"
        color="primary"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/subordinate-management"}
        icon="hero-user-group"
        text="下线管理"
        color="primary"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-bans"}
        icon="hero-no-symbol"
        text="封禁管理"
        color="primary"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-devices"}
        icon="hero-device-phone-mobile"
        text="设备管理"
        color="primary"
      />
    </.sidebar_section>
    
<!-- 活动系统 -->
    <.sidebar_section id="activity-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-gift" class="size-4 text-secondary" />
          <span class="font-semibold">活动系统</span>
        </div>
      </:label>
      <!-- 日常活动 -->
      <.sidebar_subgroup title="日常活动">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/daily-game-tasks"}
          icon="hero-check-circle"
          text="每日任务"
          color="secondary"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/seven-day-logins"}
          icon="hero-calendar-days"
          text="七日登录"
          color="secondary"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/weekly-cards"}
          icon="hero-ticket"
          text="周卡活动"
          color="secondary"
        />
      </.sidebar_subgroup>
      
<!-- 充值活动 -->
      <.sidebar_subgroup title="充值活动">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/recharge-tasks"}
          icon="hero-currency-dollar"
          text="充值任务"
          color="warning"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/first-recharge-gifts"}
          icon="hero-gift-top"
          text="首充礼包"
          color="warning"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/vip-gifts"}
          icon="hero-star"
          text="VIP礼包"
          color="warning"
        />
      </.sidebar_subgroup>
      
<!-- 游戏活动 -->
      <.sidebar_subgroup title="游戏活动">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/recharge-wheels"}
          icon="hero-arrow-path"
          text="转盘抽奖"
          color="accent"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/scratch-card-activities"}
          icon="hero-squares-plus"
          text="刮刮卡"
          color="accent"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/loss-rebates"}
          icon="hero-arrow-uturn-left"
          text="亏损返利"
          color="accent"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/user-loss-rebates"}
          icon="hero-arrow-uturn-left"
          text="用户返利记录"
          color="accent"
        />
      </.sidebar_subgroup>
      
<!-- 推广活动 -->
      <.sidebar_subgroup title="推广活动">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/invite-cash-activities"}
          icon="hero-megaphone"
          text="邀请奖励"
          color="info"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/binding-rewards"}
          icon="hero-link"
          text="绑定奖励"
          color="info"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/free-bonus-tasks"}
          icon="hero-hand-raised"
          text="免费任务"
          color="info"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/cdkeys"}
          icon="hero-key"
          text="CDKey配置"
          color="info"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/cdkeys-claim-records"}
          icon="hero-key"
          text="CDKey领取记录"
          color="info"
        />
      </.sidebar_subgroup>
    </.sidebar_section>
    
<!-- 活动记录 -->
    <.sidebar_section id="activity-records">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-clipboard-document-list" class="size-4 text-neutral" />
          <span class="font-semibold">活动记录</span>
        </div>
      </:label>
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/reward-claim-records"}
        icon="hero-gift"
        text="奖励记录"
        color="neutral"
      />
    </.sidebar_section>
    
<!-- 游戏管理 -->
    <.sidebar_section id="game-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-puzzle-piece" class="size-4 text-error" />
          <span class="font-semibold">游戏管理</span>
        </div>
      </:label>
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/games"}
        icon="hero-squares-2x2"
        text="游戏列表"
        color="error"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/games-backpex"}
        icon="hero-rectangle-stack"
        text="游戏管理 2.0"
        color="error"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/rooms-backpex"}
        icon="hero-building-office"
        text="房间管理 2.0"
        color="error"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/game-records"}
        icon="hero-clipboard-document-list"
        text="游戏记录"
        color="error"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/robots"}
        icon="hero-cpu-chip"
        text="机器人管理"
        color="error"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/luck-management"}
        icon="hero-sparkles"
        text="幸运值管理"
        color="warning"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/jackpots"}
        icon="hero-trophy"
        text="奖池管理"
        color="warning"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/inventory_control/"}
        icon="hero-chart-pie"
        text="游戏统计"
        color="info"
      />
    </.sidebar_section>
    
<!-- 支付系统 -->
    <.sidebar_section id="payment-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-credit-card" class="size-4 text-success" />
          <span class="font-semibold">支付系统</span>
        </div>
      </:label>
      <!-- 支付配置 -->
      <.sidebar_subgroup title="支付配置">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/payment-configs"}
          icon="hero-credit-card"
          text="支付配置"
          color="success"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/payment-gateways"}
          icon="hero-server"
          text="支付网关"
          color="success"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/bank-configs"}
          icon="hero-building-library"
          text="银行配置"
          color="success"
        />
      </.sidebar_subgroup>
      
<!-- 提现管理 -->
      <.sidebar_subgroup title="提现管理">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/withdrawal-configs"}
          icon="hero-cog-6-tooth"
          text="提现配置"
          color="warning"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/withdrawal-records"}
          icon="hero-clipboard-document-list"
          text="提现记录"
          color="warning"
        />
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/user-bank-cards"}
          icon="hero-credit-card"
          text="用户银行卡"
          color="warning"
        />
      </.sidebar_subgroup>
      
<!-- 订单管理 -->
      <.sidebar_subgroup title="订单管理">
        <.sidebar_subitem
          current_url={@current_url}
          navigate={~p"/admin/payment-orders"}
          icon="hero-shopping-cart"
          text="支付订单"
          color="info"
        />
      </.sidebar_subgroup>
    </.sidebar_section>
    
<!-- 商品系统 -->
    <.sidebar_section id="shop-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-shopping-bag" class="size-4 text-accent" />
          <span class="font-semibold">商品系统</span>
        </div>
      </:label>
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/products"}
        icon="hero-cube"
        text="商品管理"
        color="accent"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/product-templates"}
        icon="hero-document-duplicate"
        text="商品模板"
        color="accent"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/user-purchases"}
        icon="hero-receipt-percent"
        text="购买记录"
        color="accent"
      />
    </.sidebar_section>
    
<!-- 系统管理 -->
    <.sidebar_section id="system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-cog-6-tooth" class="size-4 text-neutral" />
          <span class="font-semibold">系统管理</span>
        </div>
      </:label>
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/system-config"}
        icon="hero-adjustments-horizontal"
        text="系统配置"
        color="neutral"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/channels"}
        icon="hero-globe-alt"
        text="渠道管理"
        color="neutral"
      />
      <.sidebar_item
        current_url={@current_url}
        navigate={~p"/admin/logs"}
        icon="hero-document-text"
        text="系统日志"
        color="neutral"
      />
    </.sidebar_section>
    
<!-- 侧边栏底部信息 -->
    <div class="mt-auto p-4 border-t border-base-300 bg-gradient-to-t from-base-200/30 to-transparent">
      <div class="space-y-3">
        <!-- 系统信息 -->
        <div class="text-center">
          <div class="text-xs font-semibold text-base-content/80 mb-1">Teen Patti Admin</div>
          <div class="text-xs text-base-content/50">v1.0.0</div>
        </div>
        
<!-- 系统状态 -->
        <div class="flex items-center justify-center gap-2 p-2 bg-base-100/50 rounded-lg">
          <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span class="text-xs text-base-content/70 font-medium">系统运行正常</span>
        </div>
        
<!-- 快速统计 -->
        <div class="grid grid-cols-2 gap-2 text-center">
          <div class="p-2 bg-primary/10 rounded-lg">
            <div class="text-xs font-bold text-primary">1,234</div>
            <div class="text-xs text-base-content/60">在线用户</div>
          </div>
          <div class="p-2 bg-success/10 rounded-lg">
            <div class="text-xs font-bold text-success">99.9%</div>
            <div class="text-xs text-base-content/60">系统稳定</div>
          </div>
        </div>
      </div>
    </div>
  </:sidebar>
  
<!-- Flash消息 -->
  <Backpex.HTML.Layout.flash_messages flash={@flash} />
  
<!-- 主内容区域 - 动态填充布局 -->
  <div class="flex-1 flex flex-col min-h-0 bg-gradient-to-br from-base-100 to-base-200">
    <div class="flex-1 flex flex-col p-4 lg:p-6 overflow-auto">
      <div class="flex-1 w-full max-w-none">
        {@inner_content}
      </div>
    </div>
  </div>
</Backpex.HTML.Layout.app_shell>
