defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.CrashMessageBuilder do
  @moduledoc """
  Crash游戏消息构建器

  统一处理所有客户端协议消息的构建，减少重复代码
  完全符合TpMasterClient前端框架的协议格式
  """

  alias Cy<PERSON><PERSON>ina.Teen.GameSystem.Games.Crash.{CrashGame, CrashLogic}
  require Logger

  # 主协议ID - 使用5作为游戏协议主ID（与Teen Patti保持一致）
  @main_id 5

  # 协议子ID定义 - 严格按照C++版本，保持兼容性
  @protocol_ids %{
    # 服务端发送协议（对应C++的SUB_S_*）
    # 游戏空闲状态（SUB_S_GAME_FREE）
    game_free: 1001,
    # 游戏开始（SUB_S_GAME_START）
    game_start: 1002,
    # 下注成功（SUB_S_PLACE_JETTON）- 实时广播给所有玩家
    place_bet_success: 1003,
    # 下车成功（SUB_S_ROCK_DOWN）
    cash_out_success: 1004,
    # 游戏结束（SUB_S_GAME_END）
    game_end: 1005,
    # 游戏记录（SUB_S_SEND_RECORD）
    game_record: 1006,
    #  @error_codes
    game_error: 1007,
    # 时间通知（SUB_S_TIME_NOTIFY）
    time_notify: 1008,
    # 开始飞行（SUB_S_FLY_START）
    fly_start: 1009,
    # 玩家数量变化（SUB_S_PLAYER_COUNT）
    player_count: 1010,
    # 玩家列表响应（SUB_S_PLAYER_LIST）
    player_list_response: 1012,
    # 游戏配置（SUB_S_GAME_CONFIG）- 专门的配置协议
    game_config: 1030,

    # 客户端发送协议（对应C++的SUB_C_*）
    # 客户端下注（SUB_C_PLACE_JETTON）
    client_place_bet: 1000,
    # 客户端玩家列表请求（SUB_C_PLAYER_LIST）
    client_player_list_request: 1011,
    # 客户端下车（SUB_C_ROCK_DOWN）
    client_cash_out: 1020
  }

  @doc """
  构建基础协议消息结构
  """
  def build_message(sub_id, data, opts \\ []) do
    base_message = %{
      "mainId" => @main_id,
      "subId" => sub_id,
      "data" => data
    }

    if opts[:with_timestamp] do
      Map.put(base_message, "timestamp", DateTime.utc_now() |> DateTime.to_unix(:millisecond))
    else
      base_message
    end
  end

  @doc """
  构建游戏空闲状态消息（对应C++的CMD_S_FreeState）
  """
  def build_game_free(state) do
    config = state.game_data.config
    time_leave = Map.get(config, :free_time, 5)
    current_time = System.system_time(:millisecond)

    data = %{
      # 剩余时间（秒）
      "cbTimeLeave" => time_leave,
      # 等待开始时间戳（毫秒）- 用于重连时计算正确的倒计时
      "freeStartTime" => Map.get(state.game_data, :waiting_start_time, current_time)
    }

    build_message(@protocol_ids.game_free, data, with_timestamp: true)
  end

  @doc """
  构建游戏开始消息（对应C++的CMD_S_GameStart）
  """
  def build_game_start(state) do
    config = state.game_data.config
    time_leave = Map.get(config, :betting_time, 15)
    current_time = System.system_time(:millisecond)

    data = %{
      # 下注剩余时间（秒）
      "cbTimeLeave" => time_leave,
      # 游戏轮次ID
      "roundId" => Map.get(state.game_data, :round, 1),
      # 下注开始时间戳（毫秒）- 用于重连时计算正确的倒计时
      "bettingStartTime" => Map.get(state.game_data, :betting_start_time, current_time)
    }

    build_message(@protocol_ids.game_start, data, with_timestamp: true)
  end

  # 注意：游戏状态通过 game_free/game_start/game_end 等协议传递，无需单独的 game_status 协议

  @doc """
  构建游戏配置消息（专门的1030协议）
  """
  def build_game_config(state) do
    config = state.game_data.config

    data = %{
      "chips" => Map.get(config, :chips, [500, 1000, 5000, 10000, 50000, 500_000]),
      "minBet" => Map.get(config, :min_bet, 500),
      "maxBet" => Map.get(config, :max_bet, 1_000_000),
      "betMultiple" => Map.get(config, :bet_multiple, 100),
      "revenueRatio" => Map.get(config, :revenue_ratio, 50),
      "timeLimit" => %{
        "betting" => Map.get(config, :betting_time, 15),
        "flying" => Map.get(config, :max_fly_time, 110_460),
        "settling" => Map.get(config, :settling_time, 5)
      },
      "roundId" => Map.get(state.game_data, :round, 1)
    }

    # 使用专门的1030协议发送配置信息
    build_message(@protocol_ids.game_config, data)
  end

  @doc """
  构建游戏状态消息 (类似LongHu的game_state)
  根据当前游戏阶段发送对应的协议，重连时使用
  """
  def build_game_state(state) do
    # 根据游戏阶段选择合适的协议和数据
    case state.game_data.phase do
      :waiting ->
        build_game_free(state)

      :betting ->
        build_game_start(state)

      :flying ->
        build_fly_start(state)

      :settling ->
        # 结算阶段发送游戏结束协议
        crash_multiplier = Map.get(state.game_data, :crash_multiplier, 100)
        crash_time = Map.get(state.game_data, :crash_time, 0)
        config = state.game_data.config
        settling_time = Map.get(config, :settling_time, 5)
        build_game_end(crash_time, crash_multiplier, settling_time)

      _ ->
        # 默认发送空闲状态
        build_game_free(state)
    end
  end

  @doc """
  构建下注成功消息（对应C++的CMD_S_PlaceJetton）
  """
  def build_place_bet_success(player, bet_amount, left_score, game_stats \\ %{}) do
    # 获取玩家名字
    player_name =
      case player do
        %Cypridina.Teen.GameSystem.PlayerData{} = p ->
          Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)

        %{nickname: nickname} when is_binary(nickname) ->
          nickname

        _ ->
          "玩家#{Map.get(player, :numeric_id, 0)}"
      end

    data = %{
      # 用户位置
      "wChairID" => player.numeric_id,
      # 下注数目
      "lBetScore" => bet_amount,
      # 剩余金币
      "lLeftScore" => left_score,
      # 玩家名字
      "nickname" => player_name,
      # 本轮所有玩家下注总额
      "lTotalBetAmount" => Map.get(game_stats, :total_bet_amount, 0),
      # 用户自己本轮总下注金额
      "lPlayerTotalBet" => Map.get(player, :bet_amount, bet_amount),
      # 自动下车倍率（x100，如250表示2.5x）
      "autoCashOut" => Map.get(player, :auto_cash_out) || 0
    }

    build_message(@protocol_ids.place_bet_success, data, with_timestamp: true)
  end

  @doc """
  构建下注失败消息（对应C++的CMD_S_PlaceBetFail）
  """
  def build_place_bet_fail(error_message) do
    data = %{
      # 错误消息（中文）
      "errorMessage" => error_message,
      # 服务器时间戳（毫秒）
      "serverTime" => System.system_time(:millisecond)
    }

    build_message(@protocol_ids.game_error, data)
  end

  @doc """
  构建下车成功消息（对应C++的CMD_S_RockDown）
  """
  def build_cash_out_success(
        player,
        down_time,
        down_multiplier,
        payout_amount,
        left_score,
        game_stats \\ %{}
      ) do
    # 获取玩家名字
    player_name =
      case player do
        %Cypridina.Teen.GameSystem.PlayerData{} = p ->
          Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)

        %{nickname: nickname} when is_binary(nickname) ->
          nickname

        _ ->
          "玩家#{Map.get(player, :numeric_id, 0)}"
      end

    data = %{
      # 用户位置（前端期望的字段名）
      "wChairID" => player.numeric_id,
      # 下车时间（相对于飞行开始的毫秒数）
      "nDownTime" => down_time,
      # 下车点数
      "nDownPoint" => down_multiplier,
      # 下车所得金额
      "lScore" => payout_amount,
      # 玩家剩余金币
      "lLeftScore" => left_score,
      # 玩家名字
      "nickname" => player_name,
      # 本轮所有玩家下注总额
      "lTotalBetAmount" => Map.get(game_stats, :total_bet_amount, 0),
      # 本轮所有已下车玩家获得总额
      "lTotalCashOutAmount" => Map.get(game_stats, :total_cash_out_amount, 0),
      # 本轮上车总人数
      "nTotalBetPlayers" => Map.get(game_stats, :total_bet_players, 0),
      # 本轮下车总人数
      "nTotalCashOutPlayers" => Map.get(game_stats, :total_cash_out_players, 0)
    }

    build_message(@protocol_ids.cash_out_success, data, with_timestamp: true)
  end

  @doc """
  构建飞行开始消息（对应C++的SUB_S_FLY_START）
  """
  def build_fly_start(state) do
    current_time = System.system_time(:millisecond)

    data = %{
      # 飞行开始时间戳（毫秒）- 用于倍数计算和重连同步
      "nStartTime" => Map.get(state.game_data, :flying_start_time, current_time)
    }

    build_message(@protocol_ids.fly_start, data, with_timestamp: true)
  end

  @doc """
  构建游戏结束消息（对应C++的CMD_S_GameEnd）
  """
  def build_game_end(explode_time, explode_point, time_leave) do
    data = %{
      # 爆炸时间（相对于飞行开始的毫秒数）
      "nExplodeTime" => explode_time,
      # 爆炸点数（倍数x100）
      "nExplodePoint" => explode_point,
      # 结算剩余时间（秒）
      "cbTimeLeave" => time_leave
    }

    build_message(@protocol_ids.game_end, data, with_timestamp: true)
  end

  @doc """
  构建时间通知消息（对应C++的CMD_S_TimeNotify）
  """
  def build_time_notify(start_time) do
    data = %{
      # 游戏开始时间（时间戳）
      "nStartTime" => start_time
    }

    build_message(@protocol_ids.time_notify, data, with_timestamp: true)
  end

  # 注意：下注同步通过 place_bet_success 协议实现，错误处理通过 game_error 协议实现

  # 注意：倍数更新通过 fly_start 协议实现，无需单独的 multiplier_update 协议

  @doc """
  构建游戏结束消息 (类似LongHu的settlement) - 重载版本
  """
  def build_game_settlement(state, crash_multiplier, player_results) do
    data = %{
      "roundId" => Map.get(state.game_data, :round, 1),
      "crashMultiplier" => crash_multiplier,
      "crashTime" => Map.get(state.game_data, :crash_time, 0),
      "playerResults" => player_results,
      "nextRoundIn" => Map.get(state.game_data.config, :settling_time, 5),
      "timestamp" => System.system_time(:millisecond)
    }

    build_message(@protocol_ids.game_end, data)
  end

  @doc """
  构建游戏记录消息
  发送100条记录，只包含crash_multiplier字段，数组按从新到旧排序
  """
  def build_game_record(records) do
    data = %{
      "records" =>
        Enum.map(records, fn record ->
          # 只发送crash_multiplier字段
          record.crash_multiplier
        end)
    }

    build_message(@protocol_ids.game_record, data)
  end

  @doc """
  构建玩家数量变化消息（SUB_S_PLAYER_COUNT）
  """
  def build_player_count_change(total_players) do
    data = %{
      "totalplayernum" => total_players
    }

    build_message(@protocol_ids.player_count, data)
  end

  @doc """
  构建玩家列表响应消息
  """
  def build_player_list_response(state, player_list, betrank_data, winscorerank_data) do
    data = %{
      "totalplayernum" => map_size(state.players),
      # "betrank" => betrank_data,
      "playerlist" => player_list,
      "winscorerank" => winscorerank_data
    }

    build_message(@protocol_ids.player_list_response, data)
  end

  @doc """
  获取客户端协议ID映射
  """
  def client_protocols do
    %{
      place_bet: @protocol_ids.client_place_bet,
      cash_out: @protocol_ids.client_cash_out,
      player_list_request: @protocol_ids.client_player_list_request
    }
  end

  # ==================== 私有辅助函数 ====================

  # 计算剩余时间
  defp calculate_time_leave(state) do
    case state.game_data.phase do
      :waiting -> Map.get(state.game_data.config, :free_time, 5)
      :betting -> Map.get(state.game_data.config, :betting_time, 15)
      :settling -> Map.get(state.game_data.config, :settling_time, 5)
      # 飞行阶段没有固定时间限制
      :flying -> 0
      _ -> 0
    end
  end

  # 获取倍数历史记录
  defp get_multiplier_history(state) do
    Map.get(state.game_data, :multiplier_history, [])
    # 最近10次记录
    |> Enum.take(-10)
  end

  defp get_time_left(state) do
    current_time = System.system_time(:millisecond)
    config = state.game_data.config

    case state.game_data.phase do
      :waiting ->
        start_time = Map.get(state.game_data, :waiting_start_time, current_time)
        free_time = Map.get(config, :free_time, 5) * 1000
        max(0, free_time - (current_time - start_time))

      :betting ->
        start_time = Map.get(state.game_data, :betting_start_time, current_time)
        betting_time = Map.get(config, :betting_time, 15) * 1000
        max(0, betting_time - (current_time - start_time))

      :flying ->
        start_time = Map.get(state.game_data, :flying_start_time, current_time)
        crash_time = Map.get(state.game_data, :crash_time, 110_460)
        max(0, crash_time - (current_time - start_time))

      :settling ->
        start_time = Map.get(state.game_data, :settling_start_time, current_time)
        settling_time = Map.get(config, :settling_time, 5) * 1000
        max(0, settling_time - (current_time - start_time))

      _ ->
        0
    end
  end

  defp get_game_state_code(phase) do
    case phase do
      :waiting -> 0
      :betting -> 1
      :flying -> 2
      :settling -> 3
      _ -> 0
    end
  end

  defp get_current_multiplier(state) do
    case state.game_data.phase do
      :flying ->
        current_time = System.system_time(:millisecond)
        flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
        elapsed_time = current_time - flying_start_time
        config = state.game_data.config
        CrashLogic.get_current_multiplier(elapsed_time, config)

      # 1.00x
      _ ->
        100
    end
  end

  @doc """
  获取协议ID映射
  """
  def protocol_ids, do: @protocol_ids


end
