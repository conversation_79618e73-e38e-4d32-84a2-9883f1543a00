defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase do
  @moduledoc """
  房间基础模块 - 提供通用房间逻辑的抽象

  功能：
  - 玩家进入/离开管理
  - 房间状态管理
  - 消息广播
  - 游戏生命周期管理
  - 房间日志过滤和分离
  """

  alias Cypridina.RoomSystem.RoomLogFilter
  alias Cypridina.Teen.GameSystem.{PlayerData, PlayerDataBuilder}
  alias Teen.Events.EventPublisher

  defmacro __using__(opts) do
    quote do
      use GenServer
      require Logger

      @behaviour Cypridina.RoomSystem.RoomBehaviour

      alias Cypridina.RoomSystem.RoomManager
      alias CypridinaWeb.GameChannel

      @registry_name :game_room_registry
      @game_type unquote(opts[:game_type])
      # 房间状态枚举
      @room_states %{
        # 等待玩家
        waiting: :waiting,
        # 游戏中
        playing: :playing,
        # 结算中
        settling: :settling,
        # 已结束
        finished: :finished
      }

      # 客户端API
      def start_link(room_spec) do
        GenServer.start_link(__MODULE__, room_spec,
          name: {:via, Registry, {@registry_name, room_spec.id}}
        )
      end

      def join_room(room_id, user_id, bring_data \\ %{}) do
        RoomManager.call_room(room_id, {:join_room, user_id, bring_data})
      end

      def leave_room(room_id, user_id) do
        RoomManager.call_room(room_id, {:leave_room, user_id})
      end

      def send_game_message(room_id, user_id, message) do
        RoomManager.send_to_room(room_id, {:game_message, user_id, message})
      end

      # GenServer回调
      @impl true
      def init(room_spec) do
        Logger.info("🏠 [ROOM_BASE] 初始化房间: #{room_spec.id}, 游戏类型: #{@game_type}")

        initial_state = %{
          id: room_spec.id,
          trace_id: Map.get(room_spec, :trace_id),
          game_type: @game_type,
          game_id: Map.get(room_spec, :game_id),
          topic: Map.get(room_spec, :topic),
          config: room_spec.config,
          creator_id: room_spec.creator_id,
          created_at: room_spec.created_at,
          room_state: @room_states.waiting,
          players: %{},
          max_players: Map.get(room_spec.config, :max_players, 8),
          min_players: Map.get(room_spec.config, :min_players, 2),
          game_data: %{},
          last_activity: DateTime.utc_now()
        }

        # 调用具体游戏的初始化逻辑
        state = init_game_logic(initial_state)

        # 设置房间超时检查
        # schedule_timeout_check()

        Cypridina.Ledger.BalanceCache.subscribe_jackpot_updates(
          fn game_id, jackpot_id, old_balance, new_balance ->
            Logger.info(
              "💰 [ROOM_BASE] 收到奖池更新: #{game_id}, #{jackpot_id}, #{old_balance} -> #{new_balance}"
            )

            # 调用具体游戏的奖池更新逻辑
            on_jackpot_updated(state, jackpot_id, new_balance)
          end,
          initial_state.game_id
        )

        # 日志
        # 设置Logger metadata，包含room_id
        room_id = room_spec.id
        Logger.metadata(room_id: room_id, game_id: initial_state.game_id)

        # 创建房间专用日志过滤器
        case RoomLogFilter.create_room_logger(%{game_id: initial_state.game_id, room_id: room_id}) do
          {:ok, _handler_id} ->
            Logger.info("🏠 [ROOM_BASE] 房间日志过滤器创建成功: #{room_id}")

          {:error, reason} ->
            Logger.warning("🏠 [ROOM_BASE] 房间日志过滤器创建失败: #{room_id}, 原因: #{inspect(reason)}")
        end

        {:ok, state}
      end

      @impl true
      def handle_call(:get_state, _from, state) do
        {:reply, state, state}
      end

      @impl true
      def handle_call({:join_room, user_id, bring_data}, from, state) do
        new_state = handle_player_join(state, user_id, bring_data, from)
        {:noreply, new_state}
      end

      @impl true
      def handle_call({:leave_room, user_id}, _from, state) do
        {result, new_state} = handle_player_leave(state, user_id)
        {:reply, result, new_state}
      end

      @impl true
      def handle_cast({:game_message, user_id, message}, state) do
        player = get_player_by_user_id(state, user_id)

        # 检查是否为房间通用协议，如果是则在room_base中统一处理
        new_state =
          case message do
            %{"mainId" => 4, "subId" => 158} ->
              handle_tip_dealer_message(state, player, message)

            %{"mainId" => 4, "subId" => 10} ->
              handle_zanli_reconnect_message(state, player, message)

            _ ->
              handle_game_message(state, player, message)
          end

        {:noreply, new_state}
      end

      @impl true
      def handle_cast({:broadcast, message}, state) do
        broadcast_to_room(state, message)
        {:noreply, state}
      end

      @impl true
      def handle_info({:execute_on_player_joined, player}, state) do
        # 异步执行游戏加入逻辑，避免阻塞Phoenix Channel响应
        Logger.info("🚀 [ROOM_BASE] 异步执行玩家加入逻辑: #{player.numeric_id}")

        updated_state = on_player_joined(state, player)
        {:noreply, updated_state}
      end

      @impl true
      def handle_info(:timeout_check, state) do
        new_state = check_room_timeout(state)
        schedule_timeout_check()
        {:noreply, new_state}
      end

      @impl true
      def handle_info(:game_tick, state) do
        new_state = handle_game_tick(state)
        {:noreply, new_state}
      end

      @impl true
      def handle_info(:close_room, state) do
        Logger.info("🏠 [ROOM_BASE] 关闭房间: #{state.id}")
        {:stop, :normal, state}
      end

      @impl true
      def terminate(reason, state) do
        case state do
          %{id: room_id} = valid_state ->
            Logger.error("🏠 [ROOM_BASE] 房间进程终止: #{room_id}, 原因: #{inspect(reason)}")

            # 立即清理房间内所有机器人状态
            cleanup_robots_on_terminate(state)

            # 广播房间销毁事件给room_manager
            Phoenix.PubSub.broadcast(
              Cypridina.PubSub,
              "room_events",
              {:room_terminated, state.id, reason}
            )

            # 确保清理房间日志过滤器
            RoomLogFilter.remove_room_logger(state.id)

          invalid_state ->
            Logger.error(
              "🏠 [ROOM_BASE] 房间进程终止，但状态无效: #{inspect(invalid_state)}, 原因: #{inspect(reason)}"
            )
        end

        :ok
      end

      # 房间终止时清理机器人状态
      defp cleanup_robots_on_terminate(state) do
        # 获取房间内所有机器人
        robot_players =
          state.players
          |> Enum.filter(fn {_id, player} -> player.is_robot end)
          |> Enum.map(fn {id, _player} -> id end)

        if length(robot_players) > 0 do
          Logger.info("🤖 [ROOM_BASE] 房间终止，清理 #{length(robot_players)} 个机器人状态")

          # 先尝试释放真实机器人回到系统池
          try do
            Teen.RobotManagement.SimpleRobotProvider.release_robots(robot_players)
            Logger.info("🤖 [ROOM_BASE] 真实机器人已释放: #{inspect(robot_players)}")
          rescue
            error ->
              Logger.warning("🤖 [ROOM_BASE] 释放真实机器人失败: #{inspect(error)}")
          end

          # 然后使用传统方法回收机器人状态
          Enum.each(robot_players, fn robot_id ->
            try do
              Teen.RobotManagement.RobotStateManager.complete_robot_recycling(
                robot_id,
                "房间终止自动回收"
              )
            rescue
              error ->
                Logger.warning("🤖 [ROOM_BASE] 回收机器人 #{robot_id} 失败: #{inspect(error)}")
            end
          end)
        end
      end

      @impl true
      def handle_info(:start_new_round, state) do
        new_state = on_game_start(state)
        {:noreply, new_state}
      end

      # 通用房间逻辑

      # 默认的玩家加入处理逻辑 - 供具体游戏复用
      defp default_handle_player_join(state, user_id, bring_data, from) do
        player = get_player_by_user_id(state, user_id)

        cond do
          player != nil ->
            Logger.info(
              "🏠 [ROOM_BASE] 玩家重新加入: #{player.numeric_id} - #{get_player_nickname(player)} (机器人: #{player.is_robot})"
            )

            GenServer.reply(from, {:ok, :rejoined})

            state
            # 调用具体游戏的玩家加入逻辑
            |> on_player_rejoined(player)

          map_size(state.players) >= state.max_players ->
            Logger.warning("🏠 [ROOM_BASE] 房间已满: #{state.id}")
            GenServer.reply(from, {:error, :room_full})
            state

          state.room_state not in [@room_states.waiting, @room_states.playing] ->
            Logger.warning("🏠 [ROOM_BASE] 房间状态不允许加入: #{state.room_state}")
            GenServer.reply(from, {:error, :invalid_room_state})
            state

          true ->
            {:ok, user} = Cypridina.UserCache.get_user(user_id)

            Logger.info(
              "🏠 [ROOM_BASE] 玩家加入房间: #{user.numeric_id} - #{get_user_nickname(user)} -> #{state.id}"
            )

            # 使用统一的玩家数据构造方法
            player = PlayerDataBuilder.create_real_player_data(user)

            new_state = %{
              state
              | players: Map.put(state.players, user.numeric_id, player),
                last_activity: DateTime.utc_now()
            }

            # 立即回复客户端，确保 [4][94] 响应优先发送
            GenServer.reply(from, {:ok, :joined})

            # 发送匹配开始消息
            send_match_start(new_state, player)

            # 发送匹配成功消息
            send_match_success(new_state, player)

            # 异步执行游戏逻辑，避免阻塞响应
            Process.send_after(self(), {:execute_on_player_joined, player}, 0)

            # broadcast_player_enter(new_state, player)
            new_state
        end
      end

      # 可重载的玩家加入处理接口
      def handle_player_join(state, user_id, bring_data, from) do
        default_handle_player_join(state, user_id, bring_data, from)
      end

      defp get_player_by_user_id(state, user_id) do
        state.players
        |> Enum.find_value(fn {id, player} ->
          if player.user_id == user_id, do: player, else: nil
        end)
      end

      defp get_player(state, numeric_id) do
        state.players[numeric_id]
      end

      # 获取用户昵称的辅助函数
      defp get_user_nickname(user) do
        case user.profile do
          %{nickname: nickname} when is_binary(nickname) -> nickname
          _ -> "Guest#{user.numeric_id}"
        end
      end

      # 获取玩家昵称的辅助函数
      defp get_player_nickname(player) do
        case player.user do
          %{profile: %{nickname: nickname}} when is_binary(nickname) -> nickname
          # 兼容机器人数据
          %{nickname: nickname} when is_binary(nickname) -> nickname
          _ -> "Player#{player.numeric_id}"
        end
      end

      def add_player_points(state, numeric_id, amount) do
        player = Map.get(state.players, numeric_id)
        updated_player = PlayerData.add_points(state, player, amount)

        %{state | players: Map.put(state.players, numeric_id, updated_player)}
      end

      def add_player_points(state, _any, 0), do: state

      def subtract_player_points(state, numeric_id, amount) do
        player = Map.get(state.players, numeric_id)
        updated_player = PlayerData.subtract_points(state, player, amount)

        %{state | players: Map.put(state.players, numeric_id, updated_player)}
      end

      def subtract_player_points(state, _any, 0), do: state

      def get_player_points(state, %PlayerData{} = player) do
        PlayerData.get_points(player)
      end

      def get_player_points(state, numeric_id) do
        Map.get(state.players, numeric_id)
        |> PlayerData.get_points()
      end

      def get_jackpot_balance(state, jackpot_id) do
        jackpot_identifier =
          Cypridina.Ledger.AccountIdentifier.jackpot(state.game_id, jackpot_id, :XAA)

        Cypridina.Ledger.BalanceCache.get_balance(jackpot_identifier)
        |> case do
          {:ok, balance} -> balance
          {:error, _reason} -> 0
        end
      end

      def cacl_rake_amount(state, win_amount) do
        rake_percentage = state.config[:rake_percentage] || 0
        floor(win_amount * rake_percentage / 100)
      end

      def game_rake(state, player, amount) do
        if player.is_robot do
          nil
        else
          Cypridina.Ledger.game_rake(state.game_id, player.user_id, amount, [])
        end
      end

      def contribute_to_jackpot(state, jackpot_id, amount, opts \\ []) do
        Cypridina.Ledger.game_jackpot(state.game_id, jackpot_id, amount, opts)
      end

      defp check_room_close(state) do
        if map_size(state.players) == 0 do
          Logger.info("🏠 [ROOM_BASE] 房间无玩家，准备关闭: #{state.id}")
          schedule_room_close()
          %{state | room_state: @room_states.finished}
        else
          state
        end
      end

      defp check_room_timeout(state) do
        timeout_minutes = 30
        timeout_threshold = DateTime.add(DateTime.utc_now(), -timeout_minutes * 60, :second)

        if DateTime.compare(state.last_activity, timeout_threshold) == :lt do
          Logger.info("🏠 [ROOM_BASE] 房间超时，准备关闭: #{state.id}")
          schedule_room_close()
          %{state | room_state: @room_states.finished}
        else
          state
        end
      end

      # ==================== 统一消息发送系统 ====================

      @doc """
      统一的单用户消息发送 - 通过GameChannel转发
      """
      defp send_to_player(state, player, message) do
        CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
        state
      end

      @doc """
      统一的房间广播消息发送 - 通过GameChannel转发
      """
      defp broadcast_to_room(state, message) do
        topic = state.topic
        # Logger.info("广播消息 #{inspect(topic)}")
        CypridinaWeb.Endpoint.broadcast!(topic, "room_message", message)
        state
      end

      @doc """
      统一的房间广播消息发送 - 排除指定玩家
      """
      defp broadcast_to_room(state, message, exclude_players) do
        topic = state.topic
        exclude_user_ids = exclude_players |> Enum.map(& &1.user_id)
        # Logger.info("广播消息 #{inspect(topic)}, 排除玩家: #{inspect exclude_user_ids}")

        # 添加排除玩家信息到消息中
        message_with_exclude = Map.put(message, "_exclude_user_ids", exclude_user_ids)
        CypridinaWeb.Endpoint.broadcast!(topic, "room_message_exclude", message_with_exclude)
        state
      end

      # ==================== 基础游戏协议发送系统 ====================

      defp build_player_data(state, player) do
        %{
          "playerid" => player.numeric_id,
          "seat" => get_player_seat(state, player),
          "money" => get_player_points(state, player),
          "name" => get_player_name(player),
          "avatar" => get_player_avatar(player),
          "level" => get_player_level(player),
          "state" => get_player_state(state, player)
        }
      end

      @doc """
      发送房间信息给玩家 (SC_ROOM_INFO_P - MainProto.Game=4, subId=2)
      """
      defp send_room_info_to_player(state, player) do
        # 构建玩家列表
        playerlist =
          state.players
          |> Enum.map(fn {_key, player} ->
            seat = get_player_seat(state, player)

            {to_string(seat), build_player_data(state, player)}
          end)

        message = %{
          # MainProto.Game
          "mainId" => 4,
          # SC_ROOM_INFO_P
          "subId" => 2,
          "data" => %{
            "roomstate" => state.room_state,
            "playerlist" => playerlist,
            "singlechatfee" => Map.get(state.config, :single_chat_fee, 0),
            "tipdealerfee" => Map.get(state.config, :tip_dealer_fee, 0),
            "maxplayers" => state.max_players,
            "minplayers" => state.min_players
          }
        }

        send_to_player(state, player, message)
      end

      @doc """
      广播玩家进入房间 (SC_ROOM_PLAYER_ENTER_P - MainProto.Game=4, subId=12)
      """

      # defp broadcast_player_enter(state, player) do
      #   broadcast_to_room(state, %{
      #     # MainProto.Game
      #     "mainId" => 4,
      #     # SC_ROOM_PLAYER_ENTER_P
      #     "subId" => 12,
      #     "data" => build_player_data(state, player)
      #   })
      # end

      @doc """
      广播房间状态变化 (SC_ROOM_SET_STATE_P - MainProto.Game=4, subId=5)
      """
      defp broadcast_room_state(state) do
        broadcast_to_room(
          state,
          message = %{
            # MainProto.Game
            "mainId" => 4,
            # SC_ROOM_SET_STATE_P
            "subId" => 5,
            "data" => %{
              "roomstate" => state.room_state
            }
          }
        )
      end

      @doc """
      发送玩家金币更新 (SC_ROOM_RESET_COIN_P - MainProto.Game=4, subId=8)
      """
      defp send_player_money_update(state, player, new_money) do
        message = %{
          # MainProto.Game
          "mainId" => 4,
          # SC_ROOM_RESET_COIN_P
          "subId" => 8,
          "data" => %{
            "playerid" => player.numeric_id,
            "coin" => new_money,
            "type" => 1,
          }
        }

        send_to_player(state, player, message)
      end

      @doc """
      发送匹配开始 (SC_MODE1_ENTER_PIPEI_P - MainProto.Game=4, subId=94)
      """
      defp send_match_start(state, player) do
        message = %{
          # MainProto.Game
          "mainId" => 4,
          # SC_MODE1_ENTER_PIPEI_P
          "subId" => 94,
          "data" => %{
            "code" => 1,
            "msg" => "开始匹配"
          }
        }

        send_to_player(state, player, message)
      end

      @doc """
      发送匹配成功 (SC_MODE1_PIPEI_OVER_P - MainProto.Game=4, subId=95)
      """
      defp send_match_success(state, player) do
        message = %{
          # MainProto.Game
          "mainId" => 4,
          # SC_MODE1_PIPEI_OVER_P
          "subId" => 95,
          "data" => %{
            "code" => 1,
            "roomid" => state.id,
            "msg" => "匹配成功"
          }
        }

        send_to_player(state, player, message)
      end

      @doc """
      获取玩家座位号 - 子类可重载
      """
      defp get_player_seat(state, player) do
        Logger.info("座位号获取函数被调用，玩家:[#{player.numeric_id}] ,#{player.user_id} #{inspect(player)}")
        # 默认实现：按加入顺序分配座位
        state.players
        |> Enum.to_list()
        |> Enum.find_index(fn {_user_id, p} -> p.user_id == player.user_id end)
        |> case do
          nil -> 1
          index -> index + 1
        end
      end

      @doc """
      获取玩家名称
      """
      defp get_player_name(player) do
        # 优先从 user 对象中获取 nickname
        case player do
          %{user: %{nickname: nickname}} when is_binary(nickname) and nickname != "" ->
            nickname

          %{nickname: nickname} when is_binary(nickname) and nickname != "" ->
            nickname

          _ ->
            "玩家#{player.numeric_id}"
        end
      end

      @doc """
      获取玩家头像
      """
      defp get_player_avatar(player) do
        Map.get(player, :avatar_id, 1)
      end

      @doc """
      获取玩家等级
      """
      defp get_player_level(player) do
        Map.get(player, :level, 1)
      end

      @doc """
      获取玩家状态 - 子类可重载
      """
      defp get_player_state(state, player) do
        # 默认状态：0=正常
        0
      end

      defp schedule_timeout_check() do
        # 5分钟检查一次
        Process.send_after(self(), :timeout_check, 5 * 60 * 1000)
      end

      defp schedule_room_close() do
        # 10秒后关闭
        Process.send_after(self(), :close_room, 10 * 1000)
      end

      defp start_game(state) do
        Logger.info("🏠 [ROOM_BASE] 开始游戏: #{state.id}")
        new_state = %{state | room_state: @room_states.playing}

        # 调用具体游戏的开始逻辑
        on_game_start(new_state)
      end

      # ==================== RoomBehaviour 回调函数实现 ====================

      @doc """
      获取游戏类型 - 需要在具体游戏中重写
      """
      def game_type, do: @game_type

      @doc """
      获取最小玩家数 - 默认值，可在具体游戏中重写
      """
      def min_players, do: 2

      @doc """
      获取最大玩家数 - 默认值，可在具体游戏中重写
      """
      def max_players, do: 8

      @doc """
      判断游戏是否结束 - 默认实现，可在具体游戏中重写
      """
      def game_over?(state) do
        state.room_state == @room_states.finished
      end

      @doc """
      发布游戏结束事件 - 统一的事件发布机制

      参数：
      - state: 房间状态
      - settlement_result: 结算结果，包含玩家变化等信息
      - opts: 可选参数，如游戏类型等
      """
      def publish_game_completion_events(state, settlement_result, opts \\ []) do
        game_type = Keyword.get(opts, :game_type, @game_type)
        game_round_id = generate_game_round_id(state)

        # 收集所有需要发布的事件
        events =
          collect_player_events(state, settlement_result, game_type, game_round_id)

        # 批量发布事件
        if length(events) > 0 do
          EventPublisher.publish_batch_game_completed(events)
          Logger.info("🎯 [ROOM_BASE] 发布游戏完成事件: #{length(events)} 个玩家")
        end
      end

      @doc """
      生成游戏回合ID
      """
      defp generate_game_round_id(state) do
        round = Map.get(state.game_data || %{}, :round, 1)
        "#{state.id}_#{round}"
      end

      @doc """
      从结算结果中获取胜者ID
      """
      defp get_settlement_winner(settlement_result) do
        Map.get(settlement_result, :winner, :none)
      end

      @doc """
      收集所有玩家的事件数据
      """
      defp collect_player_events(state, settlement_result, game_type, game_round_id) do
        player_changes = Map.get(settlement_result, :player_changes, %{})

        Enum.reduce(player_changes, [], fn {numeric_id, change}, acc ->
          player = get_player(state, numeric_id)

          # 跳过机器人玩家
          if player.is_robot do
            acc
          else
            event_data =
              build_player_event_data(
                state,
                numeric_id,
                change,
                game_type,
                game_round_id
              )

            [event_data | acc]
          end
        end)
      end

      @doc """
      构建单个玩家的事件数据
      """
      defp build_player_event_data(state, player_id, change, game_type, game_round_id) do
        # 获取玩家的下注金额
        bet_amount = get_player_bet_amount(state, player_id)

        # 构建游戏结果
        game_result = %{
          status: if(change > 0, do: :win, else: :loss),
          bet_amount: bet_amount,
          win_amount: max(0, change),
          loss_amount: max(0, -change),
          net_profit: change
        }

        %{
          player_id: player_id,
          game_round_id: game_round_id,
          game_result: game_result,
          game_type: game_type,
          room_id: state.id,
          timestamp: DateTime.utc_now()
        }
      end

      @doc """
      获取玩家下注金额 - 默认实现，可在具体游戏中重写
      """
      defp get_player_bet_amount(state, player_id) do
        game_data = state.game_data || %{}
        player_total_bets = Map.get(game_data, :player_total_bets, %{})
        Map.get(player_total_bets, player_id, 0)
      end

      # 需要在具体游戏中实现的回调函数
      def init_game_logic(state), do: state
      def on_player_joined(state, _player), do: state
      def on_player_rejoined(state, _player), do: state

      # 默认的玩家离开处理逻辑 - 供具体游戏复用
      defp default_handle_player_leave(state, user_id) do
        player = get_player_by_user_id(state, user_id)

        if player do
          numeric_id = player.numeric_id
          Logger.info("🏠 [ROOM_BASE] 玩家离开房间: #{numeric_id} <- #{state.id}")

          new_state =
            state
            # 调用具体游戏的玩家离开逻辑
            |> on_player_left(player)
            # 检查房间是否应该关闭
            |> check_room_close()

          {{:ok, :left}, new_state}
        else
          Logger.warning("🏠 [ROOM_BASE] 玩家不在房间中")
          {{:error, :not_in_room}, state}
        end
      end

      # ==================== 打赏荷官通用处理 ====================

      # 处理打赏荷官消息 - 所有游戏通用
      defp handle_tip_dealer_message(state, player, %{"data" => data} = message) do
        Logger.info(
          "💰 [TIP_DEALER] 房间#{state.id} 收到打赏荷官请求 - 玩家: #{player.numeric_id}, 数据: #{inspect(data)}"
        )

        # 获取房间配置中的打赏费用
        tip_fee = Map.get(state.config, :tip_dealer_fee, 100)
        Logger.info("💰 [TIP_DEALER] 打赏费用: #{tip_fee}")

        # 检查玩家金币是否足够
        player_money = get_player_points(state, player.numeric_id)

        if player_money >= tip_fee do
          Logger.info("💰 [TIP_DEALER] 玩家金币充足，执行打赏 - 费用: #{tip_fee}, 余额: #{player_money}")

          # 扣除玩家金币
          new_state = subtract_player_points(state, player.numeric_id, tip_fee)

          # 获取扣除后的金币数量
          current_money = get_player_points(new_state, player.numeric_id)

          # 发送金币更新协议给玩家
          send_player_money_update(new_state, player, current_money)

          # 🎯 额外发送金币变更通知协议 [7][0] 确保前端正确更新
          send_money_change_notification(new_state, player, current_money)

          # 广播打赏荷官消息给房间内所有玩家
          broadcast_tip_dealer_message(new_state, player, tip_fee)

          Logger.info(
            "💰 [TIP_DEALER] 打赏荷官成功 - 玩家: #{player.numeric_id}, 费用: #{tip_fee}, 剩余: #{current_money}"
          )

          new_state
        else
          Logger.warning("💰 [TIP_DEALER] 玩家金币不足 - 需要: #{tip_fee}, 拥有: #{player_money}")

          # 发送金币不足错误消息
          send_tip_dealer_error(state, player, "金币不足，无法打赏荷官")
          state
        end
      end

      # 处理没有data字段的打赏消息
      defp handle_tip_dealer_message(state, player, message) do
        handle_tip_dealer_message(state, player, Map.put(message, "data", %{}))
      end

      # ==================== 暂离重连通用处理 ====================

      # 处理暂离重连消息 - 所有游戏通用
      defp handle_zanli_reconnect_message(state, player, _message) do
        Logger.info("🔄 [ZANLI_RECONNECT] 房间#{state.id} 收到暂离重连请求 - 玩家: #{player.numeric_id}")

        # 检查玩家是否在暂离状态
        player_data = Map.get(state.players, player.numeric_id)
        is_zanli = player_data && Map.get(player_data, :zanli_mode, false)

        Logger.info("🔄 [ZANLI_RECONNECT] 玩家当前暂离状态: #{is_zanli}")

        if is_zanli do
          Logger.info("🔄 [ZANLI_RECONNECT] ✅ 玩家退出暂离模式 - 玩家: #{player.numeric_id}")
          exit_zanli_mode(state, player.numeric_id)
        else
          Logger.info("🔄 [ZANLI_RECONNECT] ⚠️ 玩家不在暂离状态，忽略暂离重连请求 - 玩家: #{player.numeric_id}")
          state
        end
      end

      # 玩家退出暂离模式
      defp exit_zanli_mode(state, player_id) do
        Logger.info("🔄 [ZANLI_EXIT] 玩家#{player_id} 退出暂离模式")

        # 更新玩家状态，清理暂离相关数据
        updated_players =
          Map.update!(state.players, player_id, fn player ->
            player
            |> Map.put(:zanli_mode, false)
            # 清理暂离局数
            |> Map.delete(:zanli_rounds)
          end)

        updated_state = %{state | players: updated_players}

        # 发送暂离回来成功协议 SC_ROOM_ZANLI_COMBACK_SUCCESS_P (MainID=4, SubID=11)
        send_zanli_comback_success_message(updated_state, player_id)

        updated_state
      end

      # 发送暂离回来成功协议 SC_ROOM_ZANLI_COMBACK_SUCCESS_P
      defp send_zanli_comback_success_message(state, player_id) do
        player = Map.get(state.players, player_id)

        # SC_ROOM_ZANLI_COMBACK_SUCCESS_P 协议 (MainID=4, SubID=11)
        message = %{
          "mainId" => 4,
          # SC_ROOM_ZANLI_COMBACK_SUCCESS_P
          "subId" => 11,
          "data" => %{
            "playerid" => player_id
          }
        }

        Logger.info("🔄 [ZANLI_COMEBACK] 玩家#{player_id} 暂离回来成功 [4][11]")

        # 发送给指定玩家
        try do
          user_id = Map.get(player, :user_id) || player_id
          CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
          Logger.info("🔄 [ZANLI_COMEBACK] ✅ 暂离回来成功协议发送成功")
        rescue
          error ->
            Logger.error("🔄 [ZANLI_COMEBACK] ❌ 暂离回来成功协议发送失败: #{inspect(error)}")
        end
      end

      # 广播打赏荷官消息
      defp broadcast_tip_dealer_message(state, player, tip_fee) do
        # 构建打赏荷官广播消息
        tip_message = %{
          "mainId" => 4,
          # SC_ROOM_TIPDEALER_P
          "subId" => 159,
          "data" => %{
            "playerid" => player.numeric_id,
            "playername" => PlayerDataBuilder.get_display_name(player),
            "tipamount" => tip_fee,
            "seatid" =>
              if Map.has_key?(player, :seat) do
                player.seat
              else
                if Map.has_key?(player, :seatid) do
                  player.seatid
                else
                  0
                end
              end,
            "seat" =>
              if Map.has_key?(player, :seat) do
                player.seat
              else
                if Map.has_key?(player, :seatid) do
                  player.seatid
                else
                  0
                end
              end,
            "msg" => "#{PlayerDataBuilder.get_display_name(player)} 打赏了荷官 #{tip_fee} 金币"
          }
        }

        broadcast_to_room(state, tip_message)
      end

      # 发送打赏荷官错误消息
      defp send_tip_dealer_error(state, player, error_msg) do
        error_message = %{
          "mainId" => 4,
          # SC_ROOM_TIPDEALER_P
          "subId" => 159,
          "data" => %{
            "code" => 1,
            "msg" => error_msg
          }
        }

        send_to_player(state, player, error_message)
      end

      # 发送金币变更通知协议 [7][0] SC_SET_MONEY_P
      defp send_money_change_notification(state, player, new_money) do
        money_change_message = %{
          # MainProto.Money
          "mainId" => 7,
          # SC_SET_MONEY_P
          "subId" => 0,
          "data" => %{
            "money" => new_money
          }
        }

        send_to_player(state, player, money_change_message)
        Logger.info("💰 [TIP_DEALER] 发送金币变更通知 [7][0] - 玩家: #{player.numeric_id}, 金币: #{new_money}")
      end

      # 可重载的玩家离开处理接口
      def handle_player_leave(state, user_id) do
        default_handle_player_leave(state, user_id)
      end

      # 默认的玩家离开后处理逻辑 - 供具体游戏复用
      defp default_on_player_left(state, player) do
        # 广播玩家离开消息
        %{
          state
          | players: Map.delete(state.players, player.numeric_id),
            last_activity: DateTime.utc_now()
        }
        |> broadcast_to_room(%{
          # MainProto.Game
          "mainId" => 4,
          # SC_ROOM_PLAYER_QUIT_P
          "subId" => 14,
          "data" => %{
            "playerid" => player.numeric_id,
            "seat" => get_player_seat(state, player),
            "reason" => 0
          }
        })
        # 发送退出确认给玩家
        |> send_to_player(player, %{
          # MainProto.Game
          "mainId" => 4,
          # SC_ROOM_DEL_P
          "subId" => 16,
          "data" => %{
            "code" => 1,
            "msg" => "退出成功"
          }
        })
      end

      # 可重载的玩家离开后处理接口
      def on_player_left(state, player) do
        default_on_player_left(state, player)
      end

      def on_jackpot_updated(state, jackpot_id, new_amount), do: state
      def on_game_start(state), do: state
      def handle_game_message(state, _player, _message), do: state
      def handle_game_tick(state), do: state

      # ==================== Jackpot 广播系统 ====================

      @doc """
      向指定游戏类型的所有房间广播jackpot获奖消息

      参数：
      - game_type: 游戏类型
      - sub_id: 协议子ID，用来定义具体的协议类型
      - jackpot_record: 获奖记录，包含获奖信息的映射
      """
      def broadcast_jackpot_to_all_room(game_type, sub_id, jackpot_record) do
        # 构建jackpot广播消息
        message = %{
          # MainProto.Game
          "mainId" => 5,
          "subId" => sub_id,
          "data" => jackpot_record
        }

        # 通过RoomManager广播到指定游戏类型的所有房间
        case RoomManager.broadcast_to_game_type(game_type, {:broadcast, message}) do
          {:ok, %{success: success_count, total: total_count}} ->
            Logger.info(
              "🎰 [JACKPOT_BROADCAST] Jackpot广播成功: 游戏类型=#{game_type}, 协议=#{sub_id}, 成功房间数=#{success_count}/#{total_count}"
            )

            {:ok, %{success: success_count, total: total_count}}

          {:error, reason} ->
            Logger.error(
              "🎰 [JACKPOT_BROADCAST] Jackpot广播失败: 游戏类型=#{game_type}, 原因=#{inspect(reason)}"
            )

            {:error, reason}
        end
      end

      # 智能概率调控功能
      alias Teen.Resources.Inventory.ProbabilityAdjustment

      # 获取概率调整建议 - 返回 -10 到 +10 的整数调整值
      def get_probability_adjustment(state, game_class_type) do
        # 负值：增加平台优势，降低玩家中奖率
        # 正值：增加玩家优势，提高玩家中奖率
        case ProbabilityAdjustment.get_quick_adjustment(state.game_id, game_class_type) do
          %{success: true, suggestion: suggestion} ->
            suggestion

          %{success: false} ->
            # 失败时返回默认值，不调整
            0
        end
      end

      # 获取玩家充值次数
      defp get_player_recharge_count(player) do
        Map.get(player, :recharge_count, 1)
      end

      defoverridable game_type: 0,
                     min_players: 0,
                     max_players: 0,
                     game_over?: 1,
                     init_game_logic: 1,
                     handle_player_join: 4,
                     handle_player_leave: 2,
                     on_player_joined: 2,
                     on_player_rejoined: 2,
                     on_player_left: 2,
                     on_game_start: 1,
                     on_jackpot_updated: 3,
                     handle_game_message: 3,
                     handle_game_tick: 1,
                     send_room_info_to_player: 2
    end
  end
end
