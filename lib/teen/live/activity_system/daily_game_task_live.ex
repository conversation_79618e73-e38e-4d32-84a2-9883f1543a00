defmodule Teen.Live.ActivitySystem.DailyGameTaskLive do
  @moduledoc """
  每日游戏任务管理页面

  使用游戏主题的配色方案和动画效果，提供丰富的视觉体验
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.GameTask
    load []
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :task_name do
        module Backpex.Fields.Text
        label("📝 任务名称")
        help_text("请输入任务名称，最多100个字符")
        searchable(true)
        orderable(true)

        render(fn assigns ->
          ~H"""
          <div class="flex items-center space-x-2">
            <span class="text-lg font-bold tracking-wide text-base-content hover:text-primary transition-colors duration-200">
              {assigns.value}
            </span>
          </div>
          """
        end)
      end

      field :game_id do
        module Backpex.Fields.Select
        label("🎮 选择游戏")
        help_text("选择关联的游戏，选择后会自动填充游戏名称")
        options(fn _assigns -> get_game_options() end)
        searchable(true)
        prompt("🎮 请选择游戏...")

        render(fn assigns ->
          ~H"""
          <div class="form-control w-full">
            <label class="label">
              <span class="label-text font-semibold">🎮 选择游戏</span>
            </label>
            <select
              name="game_id"
              id="game_id_select"
              class="select select-bordered w-full"
              phx-change="game_id_changed"
              value={assigns.value || ""}
            >
              <option value="">🎮 请选择游戏...</option>
              <%= for {label, value} <- get_game_options() do %>
                <option value={value} selected={assigns.value == value}>
                  {label}
                </option>
              <% end %>
            </select>
          </div>
          """
        end)
      end

      field :task_type do
        module Backpex.Fields.Select
        label("🎯 任务类型")
        help_text("选择任务的完成条件类型")

        options([
          {"🎮 游戏局数", "game_rounds"},
          {"💰 充值金额", "recharge_amount"},
          {"🏆 游戏胜利", "win_rounds"},
          {"🎡 转盘次数", "wheel_spins"},
          {"✅ 完成任务", "task_completion"}
        ])

        render(fn assigns ->
          case to_string(assigns.value) do
            "game_rounds" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-primary text-primary-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-primary/20">
                🎮 游戏局数
              </span>
              """

            "recharge_amount" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-success text-success-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-success/20">
                💰 充值金额
              </span>
              """

            "win_rounds" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-warning text-warning-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-warning/20 animate-pulse">
                🏆 游戏胜利
              </span>
              """

            "wheel_spins" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-secondary text-secondary-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-secondary/20">
                🎡 转盘次数
              </span>
              """

            "task_completion" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-accent text-accent-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-accent/20">
                ✅ 完成任务
              </span>
              """

            _ ->
              ~H"""
              <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium bg-gradient-to-r from-gray-400 to-slate-500 text-white shadow-md">
                ❓ 未知类型
              </span>
              """
          end
        end)
      end

      field :required_count do
        module Backpex.Fields.Number
        label("🎲 所需局数")
        help_text("完成任务需要的局数，最少1局")

        render(fn assigns ->
          ~H"""
          <span class="text-xl font-mono font-bold tabular-nums bg-primary text-primary-content px-3 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200">
            {assigns.value}
          </span>
          """
        end)
      end

      field :max_claims do
        module Backpex.Fields.Number
        label("🔄 每日最大领取次数")
        help_text("每天最多可以领取的次数，最少1次")

        render(fn assigns ->
          ~H"""
          <span class="text-xl font-mono font-bold tabular-nums bg-secondary text-secondary-content px-3 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200">
            {assigns.value}
          </span>
          """
        end)
      end

      field :target_value do
        module Backpex.Fields.Number
        label("🎯 目标值")
        help_text("任务的目标数值，如赢取金币数量等")

        render(fn assigns ->
          ~H"""
          <span class="text-xl font-mono font-bold tabular-nums bg-accent text-accent-content px-3 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200">
            {assigns.value}
          </span>
          """
        end)
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("🎁 奖励配置")
        help_text("设置任务完成后的奖励")
      end

      field :status do
        module Backpex.Fields.Select
        label("⚡ 状态")
        help_text("控制任务是否启用")

        options([
          {"✅ 启用", "enabled"},
          {"❌ 禁用", "disabled"}
        ])

        render(fn assigns ->
          case to_string(assigns.value) do
            "enabled" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-success text-success-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 animate-pulse">
                ✅ 启用中
              </span>
              """

            "disabled" ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-error text-error-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                ❌ 已禁用
              </span>
              """

            _ ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-gradient-to-r from-gray-400 to-slate-500 text-white shadow-md">
                ❓ 未知
              </span>
              """
          end
        end)
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("🚀 是否激活")
        help_text("控制任务是否对用户可见")

        render(fn assigns ->
          case assigns.value do
            true ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-info text-info-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 animate-bounce">
                🚀 活跃中
              </span>
              """

            false ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-neutral text-neutral-content shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                ⏸️ 待激活
              </span>
              """

            _ ->
              ~H"""
              <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-gradient-to-r from-gray-400 to-slate-500 text-white shadow-md">
                ❓ 未知
              </span>
              """
          end
        end)
      end

      field :start_date do
        module Backpex.Fields.Date
        label("📅 开始日期")
        help_text("任务开始的日期")

        render(fn assigns ->
          ~H"""
          <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-bold tracking-wide bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200">
            📅 {assigns.value}
          </span>
          """
        end)
      end

      field :end_date do
        module Backpex.Fields.Date
        label("📅 结束日期")
        help_text("任务结束的日期")

        render(fn assigns ->
          ~H"""
          <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-bold tracking-wide bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-200">
            📅 {assigns.value}
          </span>
          """
        end)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("⏰ 创建时间")
        readonly(true)
        only([:index, :show])
        format("%Y-%m-%d %H:%M:%S")

        render(fn assigns ->
          ~H"""
          <span class="text-xs font-mono tracking-tight text-gray-600 bg-gray-100 px-2 py-1 rounded border">
            {assigns.value}
          </span>
          """
        end)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("⏰ 更新时间")
        readonly(true)
        only([:index, :show])
        format("%Y-%m-%d %H:%M:%S")

        render(fn assigns ->
          ~H"""
          <span class="text-xs font-mono tracking-tight text-gray-600 bg-gray-100 px-2 py-1 rounded border">
            {assigns.value}
          </span>
          """
        end)
      end
    end
  end

  # 适应性主题配色方案 - 基于 DaisyUI 主题系统
  @theme_colors %{
    # 使用 DaisyUI 的语义化颜色，自动适应当前主题
    primary: "bg-primary text-primary-content",
    secondary: "bg-secondary text-secondary-content",
    accent: "bg-accent text-accent-content",
    neutral: "bg-neutral text-neutral-content",
    base: "bg-base-100 text-base-content",
    info: "bg-info text-info-content",
    success: "bg-success text-success-content",
    warning: "bg-warning text-warning-content",
    error: "bg-error text-error-content"
  }

  # 游戏主题字体样式 - 适应当前主题
  @theme_fonts %{
    # 标题字体 - 大胆、醒目，使用主题色
    title: "text-2xl font-black tracking-wide text-base-content",
    # 副标题字体 - 中等粗细，使用主题色
    subtitle: "text-lg font-bold tracking-normal text-base-content",
    # 标签字体 - 小标题样式，使用主题色
    label: "text-sm font-semibold tracking-wide uppercase text-base-content/80",
    # 内容字体 - 清晰易读，使用主题色
    content: "text-base font-medium leading-relaxed text-base-content",
    # 小字体 - 辅助信息，使用主题色
    small: "text-xs font-normal tracking-tight text-base-content/60",
    # 徽章字体 - 突出显示
    badge: "text-sm font-bold tracking-wide",
    # 数字字体 - 等宽字体，使用主题色
    number: "text-lg font-mono font-bold tabular-nums text-base-content",
    # 按钮字体 - 行动导向
    button: "text-sm font-bold tracking-wide uppercase"
  }

  @impl Backpex.LiveResource
  def singular_name, do: "🎮 每日游戏任务"

  @impl Backpex.LiveResource
  def plural_name, do: "🎮 每日游戏任务管理"

  # 添加过滤器
  @impl Backpex.LiveResource
  def filters do
    import Ash.Expr

    [
      status: %{
        module: Backpex.Filters.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        query: fn query, value ->
          Ash.Query.filter(query, expr(status == ^value))
        end
      },
      task_type: %{
        module: Backpex.Filters.Select,
        label: "任务类型",
        options: [
          {"游戏局数", :game_rounds},
          {"充值金额", :recharge_amount},
          {"游戏胜利", :win_rounds},
          {"转盘次数", :wheel_spins},
          {"完成任务", :task_completion}
        ],
        query: fn query, value ->
          Ash.Query.filter(query, expr(task_type == ^value))
        end
      },
      is_active: %{
        module: Backpex.Filters.Select,
        label: "是否激活",
        options: [
          {"激活", true},
          {"未激活", false}
        ],
        query: fn query, value ->
          Ash.Query.filter(query, expr(is_active == ^value))
        end
      }
    ]
  end

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 基本权限控制
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> true
    end
  end





  # 获取游戏选项列表
  defp get_game_options do
    try do
      # 从游戏配置中获取启用的游戏列表
      case Teen.GameManagement.ManageGameConfig.list_enabled() do
        {:ok, games} ->
          games
          |> Enum.map(fn game ->
            {"#{game.display_name} (ID: #{game.game_id})", game.game_id}
          end)
          |> Enum.sort_by(fn {label, _value} -> label end)

        {:error, _} ->
          # 如果获取失败，使用已知的游戏数据作为备选
          get_fallback_game_options()
      end
    rescue
      _error ->
        # 如果出现异常，使用已知的游戏数据作为备选
        get_fallback_game_options()
    end
  end

  # 备选游戏选项列表
  defp get_fallback_game_options do
    [
      {"Teen Patti (ID: 1)", 1},
      {"Dragon Tiger (ID: 22)", 22},
      {"Andar Bahar (ID: 30)", 30},
      {"Jhandi Munda (ID: 31)", 31},
      {"AK47 Teen Patti (ID: 32)", 32},
      {"Pot Blind (ID: 33)", 33},
      {"Safari of Wealth (ID: 34)", 34},
      {"Slot 777 (ID: 40)", 40},
      {"Slot Niu (ID: 41)", 41},
      {"Slot Cat (ID: 42)", 42}
    ]
  end
end
