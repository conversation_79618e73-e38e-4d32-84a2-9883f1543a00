defmodule Teen.Live.ActivitySystem.FirstRechargeGiftLive do
  @moduledoc """
  首充礼包管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.FirstRechargeGift
    load []
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :title do
        module Backpex.Fields.Text
        label("🎁 标题")
      end

      field :limit_days do
        module Backpex.Fields.Number
        label("📅 限制天数（注册天数）")
      end

      field :min_recharge_amount do
        module Backpex.Fields.Number
        label("💰 最小充值金额")
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("🎁 奖励配置")
      end

      field :bonus_multiplier do
        module Backpex.Fields.Number
        label("✨ 奖励倍数")
      end

      field :time_limit_hours do
        module Backpex.Fields.Number
        label("⏰ 时间限制(小时)")
      end

      field :description do
        module Backpex.Fields.Textarea
        label("📝 描述")
      end

      field :gift_description do
        module Backpex.Fields.Text
        label("📋 礼包描述")
        readonly(true)
        only([:index, :show])
      end

      field :total_reward_value do
        module Backpex.Fields.Number
        label("💎 总奖励价值")
        readonly(true)
        only([:index, :show])
      end

      field :is_time_limited do
        module Backpex.Fields.Boolean
        label("⏰ 是否有时间限制")
        readonly(true)
        only([:index, :show])
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("🔥 是否激活")
      end

      field :status do
        module Backpex.Fields.Select
        label("⚡ 状态")

        options([
          {"✅ 启用", :enabled},
          {"❌ 禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("📅 创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("📅 更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "🎁 首充礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "🎁 首充礼包"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
