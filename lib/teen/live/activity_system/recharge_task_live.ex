defmodule Teen.Live.ActivitySystem.RechargeTaskLive do
  @moduledoc """
  充值任务管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RechargeTask
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :recharge_amount do
        module Backpex.Fields.Number
        label("💰 充值金额")
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("🎁 奖励配置")
      end

      field :reward_rate do
        module Backpex.Fields.Number
        label("📊 奖励比例(%)")
        readonly(true)
        only([:index, :show])
      end

      field :task_description do
        module Backpex.Fields.Text
        label("📝 任务描述")
        readonly(true)
        only([:index, :show])
      end

      field :status do
        module Backpex.Fields.Select
        label("⚡ 状态")

        options([
          {"✅ 启用", :enabled},
          {"❌ 禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("📅 创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("📅 更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "💰 充值任务"

  @impl Backpex.LiveResource
  def plural_name, do: "💰 充值任务"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
