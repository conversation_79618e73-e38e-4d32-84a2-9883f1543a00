defmodule Teen.Live.ActivitySystem.RechargeTaskLive do
  @moduledoc """
  充值任务管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RechargeTask
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :recharge_amount do
        module Backpex.Fields.Number
        label("💰 充值金额条件")
      end

      field :recharge_type do
        module Backpex.Fields.Select
        label("📋 充值条件类型")

        options([
          {"单次充值", :single_recharge},
          {"累计充值", :cumulative_recharge},
          {"首次充值", :first_recharge},
          {"每日充值", :daily_recharge}
        ])
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("🎁 奖励配置")
        help_text("奖励配置，可以是金币、经验、道具等")
      end

      field :start_date do
        module Backpex.Fields.Date
        label("📅 开始日期")
      end

      field :end_date do
        module Backpex.Fields.Date
        label("📅 结束日期")
      end

      field :reward_rate do
        module Backpex.Fields.Number
        label("📊 奖励比例(%)")
        readonly(true)
        only([:index, :show])
      end

      field :task_description do
        module Backpex.Fields.Text
        label("📝 任务描述")
        readonly(true)
        only([:index, :show])
      end

      field :recharge_type_display do
        module Backpex.Fields.Text
        label("📋 充值条件")
        readonly(true)
        only([:index, :show])
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("🔥 当前活跃")
        readonly(true)
        only([:index, :show])
      end

      field :days_remaining do
        module Backpex.Fields.Number
        label("⏰ 剩余天数")
        readonly(true)
        only([:index, :show])
      end

      field :status do
        module Backpex.Fields.Select
        label("⚡ 状态")

        options([
          {"✅ 启用", :enabled},
          {"❌ 禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("📅 创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("📅 更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "💰 充值任务"

  @impl Backpex.LiveResource
  def plural_name, do: "💰 充值任务"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
