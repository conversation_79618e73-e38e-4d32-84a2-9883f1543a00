defmodule Teen.Live.ActivitySystem.RechargeWheelLive do
  @moduledoc """
  充值转盘管理界面
  """
  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RechargeWheel
    layout({Teen.Layouts, :admin})

    fields do
      field :wheel_name do
        module Backpex.Fields.Text
        label("转盘名称")
        searchable(true)
      end

      field :cumulative_recharge do
        module Backpex.Fields.Number
        label("累计充值金额（分）")

        format(fn value ->
          if value do
            amount = Decimal.to_float(value) / 100
            "¥#{:erlang.float_to_binary(amount, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :wheel_spins do
        module Backpex.Fields.Number
        label("转盘次数")
      end

      field :rewards do
        module Backpex.Fields.Textarea
        label("奖励配置")
        readonly(true)
        only([:index, :show])

        format(fn rewards ->
          if is_list(rewards) and length(rewards) > 0 do
            rewards
            |> Enum.map(fn reward ->
              type_name = case reward.type do
                :coins -> "金币"
                :bonus_money -> "奖金"
                :cash -> "现金"
                :vip_experience -> "VIP经验"
                :items -> "道具"
                _ -> "未知"
              end
              "#{type_name}: #{reward.amount} (#{reward.description})"
            end)
            |> Enum.join("\n")
          else
            "无奖励"
          end
        end)
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"启用", :enabled},
          {"禁用", :disabled}
        ])
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :start_date do
        module Backpex.Fields.Date
        label("开始日期")
      end

      field :end_date do
        module Backpex.Fields.Date
        label("结束日期")
      end

      field :description do
        module Backpex.Fields.Textarea
        label("活动描述")
        only([:new, :edit, :show])
      end

      # 计算字段显示
      field :status_display do
        module Backpex.Fields.Text
        label("状态")
        readonly(true)
        only([:index])
      end

      field :recharge_amount_display do
        module Backpex.Fields.Text
        label("充值金额")
        readonly(true)
        only([:index])
      end

      field :reward_summary do
        module Backpex.Fields.Text
        label("奖励摘要")
        readonly(true)
        only([:index])
      end

      field :activity_period do
        module Backpex.Fields.Text
        label("活动期间")
        readonly(true)
        only([:index, :show])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
    actions do
      action :enable_wheel do
        label("启用")
        icon("hero-check-circle")
        confirm_label("确认启用此转盘？")

        handler(fn socket, item ->
          case Teen.ActivitySystem.RechargeWheel.enable_wheel(item) do
            {:ok, _updated_item} ->
              {:ok, socket, "转盘已启用"}
            {:error, _reason} ->
              {:error, socket, "启用失败"}
          end
        end)
      end

      action :disable_wheel do
        label("禁用")
        icon("hero-x-circle")
        confirm_label("确认禁用此转盘？")

        handler(fn socket, item ->
          case Teen.ActivitySystem.RechargeWheel.disable_wheel(item) do
            {:ok, _updated_item} ->
              {:ok, socket, "转盘已禁用"}
            {:error, _reason} ->
              {:error, socket, "禁用失败"}
          end
        end)
      end

      action :activate_wheel do
        label("激活")
        icon("hero-play")
        confirm_label("确认激活此转盘？")

        handler(fn socket, item ->
          case Teen.ActivitySystem.RechargeWheel.activate_wheel(item) do
            {:ok, _updated_item} ->
              {:ok, socket, "转盘已激活"}
            {:error, _reason} ->
              {:error, socket, "激活失败"}
          end
        end)
      end

      action :deactivate_wheel do
        label("停用")
        icon("hero-pause")
        confirm_label("确认停用此转盘？")

        handler(fn socket, item ->
          case Teen.ActivitySystem.RechargeWheel.deactivate_wheel(item) do
            {:ok, _updated_item} ->
              {:ok, socket, "转盘已停用"}
            {:error, _reason} ->
              {:error, socket, "停用失败"}
          end
        end)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def plural_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      :enable_wheel -> item && item.status == :disabled
      :disable_wheel -> item && item.status == :enabled
      :activate_wheel -> item && item.is_active == false
      :deactivate_wheel -> item && item.is_active == true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index_item_actions, item) do
    ~H"""
    <div class="flex gap-2">
      <%= if item.status == :disabled do %>
        <.link
          phx-click="enable_wheel"
          phx-value-id={item.id}
          class="text-green-600 hover:text-green-800"
          title="启用转盘"
        >
          <.icon name="hero-check-circle" class="w-4 h-4" />
        </.link>
      <% else %>
        <.link
          phx-click="disable_wheel"
          phx-value-id={item.id}
          class="text-red-600 hover:text-red-800"
          title="禁用转盘"
        >
          <.icon name="hero-x-circle" class="w-4 h-4" />
        </.link>
      <% end %>

      <%= if item.is_active == false do %>
        <.link
          phx-click="activate_wheel"
          phx-value-id={item.id}
          class="text-blue-600 hover:text-blue-800"
          title="激活转盘"
        >
          <.icon name="hero-play" class="w-4 h-4" />
        </.link>
      <% else %>
        <.link
          phx-click="deactivate_wheel"
          phx-value-id={item.id}
          class="text-yellow-600 hover:text-yellow-800"
          title="停用转盘"
        >
          <.icon name="hero-pause" class="w-4 h-4" />
        </.link>
      <% end %>
    </div>
    """
  end
end
