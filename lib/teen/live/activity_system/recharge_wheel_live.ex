defmodule Teen.Live.ActivitySystem.RechargeWheelLive do
  @moduledoc """
  充值转盘管理界面
  """
  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RechargeWheel
    layout({Teen.Layouts, :admin})

    fields do
      field :name do
        module Backpex.Fields.Text
        label("转盘名称")
      end

      field :min_recharge_amount do
        module Backpex.Fields.Number
        label("最低充值金额")
      end

      field :jackpot_pool do
        module Backpex.Fields.Number
        label("奖金池")
        readonly(true)
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"启用", :enabled},
          {"禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def plural_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end
end
