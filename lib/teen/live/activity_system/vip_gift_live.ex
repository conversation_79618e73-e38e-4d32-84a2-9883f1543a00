defmodule Teen.Live.ActivitySystem.VipGiftLive do
  @moduledoc """
  VIP礼包管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.VipGift
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :vip_level do
        module Backpex.Fields.Number
        label("VIP等级")
      end

      field :daily_rewards do
        module Teen.Live.Fields.RewardField
        label("每日奖励配置")
      end

      field :weekly_rewards do
        module Teen.Live.Fields.RewardField
        label("每周奖励配置")
      end

      field :monthly_rewards do
        module Teen.Live.Fields.RewardField
        label("每月奖励配置")
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"启用", :enabled},
          {"禁用", :disabled}
        ])
      end
      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
