defmodule Teen.Live.ReportSystem.DailyReportLive do
  @moduledoc """
  系统日报表管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ReportSystem.DailyReport
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :report_date do
        module Backpex.Fields.Date
        label("📅 报表日期")
        searchable(true)
      end

      field :platform_id do
        module Backpex.Fields.Text
        label("🏢 平台ID")
        readonly(true)
        only([:index, :show])
        
        format(fn platform_id ->
          if platform_id, do: platform_id, else: "全平台"
        end)
      end

      # 用户相关指标
      field :new_devices do
        module Backpex.Fields.Number
        label("📱 新增设备")
        readonly(true)
        only([:index, :show])
      end

      field :new_registrations do
        module Backpex.Fields.Number
        label("👤 新增注册")
        readonly(true)
        only([:index, :show])
      end

      field :effective_new_users do
        module Backpex.Fields.Number
        label("✅ 有效新增")
        readonly(true)
        only([:index, :show])
      end

      field :mobile_registrations do
        module Backpex.Fields.Number
        label("📞 手机注册绑定")
        readonly(true)
        only([:show])
      end

      field :active_users do
        module Backpex.Fields.Number
        label("🔥 活跃用户")
        readonly(true)
        only([:index, :show])
      end

      field :effective_active_users do
        module Backpex.Fields.Number
        label("⭐ 有效活跃")
        readonly(true)
        only([:show])
      end

      field :active_mobile_bindings do
        module Backpex.Fields.Number
        label("📱 活跃手机绑定")
        readonly(true)
        only([:show])
      end

      # 充值相关指标
      field :recharge_users do
        module Backpex.Fields.Number
        label("💰 充值人数")
        readonly(true)
        only([:index, :show])
      end

      field :new_recharge_users do
        module Backpex.Fields.Number
        label("🆕 新增充值人数")
        readonly(true)
        only([:show])
      end

      field :first_pay_users do
        module Backpex.Fields.Number
        label("🎯 首付人数")
        readonly(true)
        only([:show])
      end

      field :recharge_amount do
        module Backpex.Fields.Text
        label("💵 新增充值额")
        readonly(true)
        only([:index, :show])
        
        format(fn amount ->
          if amount do
            value = Decimal.to_float(amount) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :withdrawal_amount do
        module Backpex.Fields.Text
        label("💸 退出总额")
        readonly(true)
        only([:show])
        
        format(fn amount ->
          if amount do
            value = Decimal.to_float(amount) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :withdrawal_users do
        module Backpex.Fields.Number
        label("👥 退出人数")
        readonly(true)
        only([:show])
      end

      # 比率相关指标
      field :withdrawal_recharge_ratio do
        module Backpex.Fields.Text
        label("📊 退充比")
        readonly(true)
        only([:show])
        
        format(fn ratio ->
          if ratio do
            "#{Decimal.to_string(ratio)}%"
          else
            "0%"
          end
        end)
      end

      field :payment_rate do
        module Backpex.Fields.Text
        label("💳 付费率")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :new_payment_rate do
        module Backpex.Fields.Text
        label("🆕 新增付费率")
        readonly(true)
        only([:show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :effective_payment_rate do
        module Backpex.Fields.Text
        label("⭐ 有效付费率")
        readonly(true)
        only([:show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :effective_new_payment_rate do
        module Backpex.Fields.Text
        label("✨ 有效新增付费率")
        readonly(true)
        only([:show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      # ARPU相关指标
      field :arpu do
        module Backpex.Fields.Text
        label("📈 ARPU")
        readonly(true)
        only([:index, :show])
        
        format(fn arpu ->
          if arpu do
            value = Decimal.to_float(arpu) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :arppu do
        module Backpex.Fields.Text
        label("📊 ARPPU")
        readonly(true)
        only([:show])
        
        format(fn arppu ->
          if arppu do
            value = Decimal.to_float(arppu) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :effective_arpu do
        module Backpex.Fields.Text
        label("⭐ 有效ARPU")
        readonly(true)
        only([:show])
        
        format(fn arpu ->
          if arpu do
            value = Decimal.to_float(arpu) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      # 留存相关指标
      field :retention_day2 do
        module Backpex.Fields.Text
        label("📅 次留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :retention_day3 do
        module Backpex.Fields.Text
        label("📅 3留")
        readonly(true)
        only([:show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :retention_day7 do
        module Backpex.Fields.Text
        label("📅 7留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :retention_day14 do
        module Backpex.Fields.Text
        label("📅 14留")
        readonly(true)
        only([:show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :retention_day30 do
        module Backpex.Fields.Text
        label("📅 30留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("📅 创建时间")
        readonly(true)
        only([:show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("📅 更新时间")
        readonly(true)
        only([:show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "📊 系统日报表"

  @impl Backpex.LiveResource
  def plural_name, do: "📊 系统日报表"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> false  # 通过生成功能创建，不允许手动新建
      :edit -> false # 报表数据不允许手动编辑
      :delete -> true
      _ -> false
    end
  end
end
