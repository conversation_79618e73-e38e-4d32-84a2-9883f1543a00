defmodule Teen.Live.ReportSystem.MonthlyReportLive do
  @moduledoc """
  系统月报表管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ReportSystem.MonthlyReport
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :report_month do
        module Backpex.Fields.Text
        label("📅 报表月份")
        searchable(true)
      end

      field :platform_id do
        module Backpex.Fields.Text
        label("🏢 平台ID")
        readonly(true)
        only([:index, :show])
        
        format(fn platform_id ->
          if platform_id, do: platform_id, else: "全平台"
        end)
      end

      # 月度汇总指标
      field :total_new_devices do
        module Backpex.Fields.Number
        label("📱 新增设备总数")
        readonly(true)
        only([:index, :show])
      end

      field :total_new_registrations do
        module Backpex.Fields.Number
        label("👤 新增注册总数")
        readonly(true)
        only([:index, :show])
      end

      field :total_effective_new_users do
        module Backpex.Fields.Number
        label("✅ 有效新增总数")
        readonly(true)
        only([:index, :show])
      end

      field :total_mobile_registrations do
        module Backpex.Fields.Number
        label("📞 手机注册绑定总数")
        readonly(true)
        only([:show])
      end

      field :total_active_users do
        module Backpex.Fields.Number
        label("🔥 活跃用户总数")
        readonly(true)
        only([:index, :show])
      end

      field :total_effective_active_users do
        module Backpex.Fields.Number
        label("⭐ 有效活跃总数")
        readonly(true)
        only([:show])
      end

      field :total_recharge_users do
        module Backpex.Fields.Number
        label("💰 充值用户总数")
        readonly(true)
        only([:index, :show])
      end

      field :total_recharge_amount do
        module Backpex.Fields.Text
        label("💵 充值总额")
        readonly(true)
        only([:index, :show])
        
        format(fn amount ->
          if amount do
            value = Decimal.to_float(amount) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :total_withdrawal_amount do
        module Backpex.Fields.Text
        label("💸 提现总额")
        readonly(true)
        only([:show])
        
        format(fn amount ->
          if amount do
            value = Decimal.to_float(amount) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      # 月度平均指标
      field :avg_payment_rate do
        module Backpex.Fields.Text
        label("💳 平均付费率")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :avg_arpu do
        module Backpex.Fields.Text
        label("📈 平均ARPU")
        readonly(true)
        only([:index, :show])
        
        format(fn arpu ->
          if arpu do
            value = Decimal.to_float(arpu) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :avg_arppu do
        module Backpex.Fields.Text
        label("📊 平均ARPPU")
        readonly(true)
        only([:show])
        
        format(fn arppu ->
          if arppu do
            value = Decimal.to_float(arppu) / 100
            "¥#{:erlang.float_to_binary(value, decimals: 2)}"
          else
            "¥0.00"
          end
        end)
      end

      field :avg_retention_day2 do
        module Backpex.Fields.Text
        label("📅 平均次留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :avg_retention_day7 do
        module Backpex.Fields.Text
        label("📅 平均7留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :avg_retention_day30 do
        module Backpex.Fields.Text
        label("📅 平均30留")
        readonly(true)
        only([:index, :show])
        
        format(fn rate ->
          if rate do
            "#{Decimal.to_string(rate)}%"
          else
            "0%"
          end
        end)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("📅 创建时间")
        readonly(true)
        only([:show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("📅 更新时间")
        readonly(true)
        only([:show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "📊 系统月报表"

  @impl Backpex.LiveResource
  def plural_name, do: "📊 系统月报表"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> false  # 通过生成功能创建，不允许手动新建
      :edit -> false # 报表数据不允许手动编辑
      :delete -> true
      _ -> false
    end
  end
end
