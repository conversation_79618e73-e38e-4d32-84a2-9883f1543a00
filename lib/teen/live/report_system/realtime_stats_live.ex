defmodule Teen.Live.ReportSystem.RealtimeStatsLive do
  @moduledoc """
  实时数据统计页面
  """

  use Phoenix.LiveView
  import Phoenix.HTML.Form

  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 每30秒更新一次实时数据
      :timer.send_interval(30_000, self(), :update_stats)
    end

    socket = 
      socket
      |> assign(:page_title, "实时数据统计")
      |> assign(:platform_id, nil)
      |> load_realtime_stats()

    {:ok, socket}
  end

  def handle_event("select_platform", %{"platform_id" => platform_id}, socket) do
    platform_id = if platform_id == "", do: nil, else: platform_id
    
    socket = 
      socket
      |> assign(:platform_id, platform_id)
      |> load_realtime_stats()

    {:noreply, socket}
  end

  def handle_event("refresh_stats", _params, socket) do
    socket = load_realtime_stats(socket)
    {:noreply, socket}
  end

  def handle_info(:update_stats, socket) do
    socket = load_realtime_stats(socket)
    {:noreply, socket}
  end

  defp load_realtime_stats(socket) do
    platform_id = socket.assigns[:platform_id]
    
    case Teen.ReportSystem.get_latest_realtime_stats(platform_id) do
      {:ok, [stats | _]} ->
        socket
        |> assign(:stats, stats)
        |> assign(:last_updated, DateTime.utc_now())
      {:ok, []} ->
        # 如果没有数据，创建默认统计
        default_stats = %{
          online_users: 0,
          active_games: 0,
          active_rooms: 0,
          realtime_recharge: Decimal.new("0"),
          realtime_withdrawal: Decimal.new("0"),
          today_new_users: 0,
          today_active_users: 0,
          today_recharge_users: 0,
          today_recharge_amount: Decimal.new("0"),
          stat_time: DateTime.utc_now()
        }
        
        socket
        |> assign(:stats, default_stats)
        |> assign(:last_updated, DateTime.utc_now())
      {:error, _reason} ->
        socket
        |> put_flash(:error, "加载实时数据失败")
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-gray-50 min-h-screen">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">📊 实时数据统计</h1>
        <p class="text-gray-600 mt-2">系统实时运营数据监控</p>
      </div>

      <!-- 平台选择和刷新按钮 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <label class="text-sm font-medium text-gray-700">选择平台：</label>
          <select 
            phx-change="select_platform" 
            name="platform_id" 
            class="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="">全平台</option>
            <!-- 这里可以动态加载平台列表 -->
          </select>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500">
            最后更新: <%= if assigns[:last_updated], do: Calendar.strftime(@last_updated, "%H:%M:%S"), else: "未知" %>
          </span>
          <button 
            phx-click="refresh_stats"
            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            🔄 刷新数据
          </button>
        </div>
      </div>

      <!-- 实时数据卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 在线用户 -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span class="text-green-600 text-lg">👥</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">在线用户</p>
              <p class="text-2xl font-semibold text-gray-900">
                <%= if assigns[:stats], do: @stats.online_users, else: 0 %>
              </p>
            </div>
          </div>
        </div>

        <!-- 活跃游戏 -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-blue-600 text-lg">🎮</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">活跃游戏</p>
              <p class="text-2xl font-semibold text-gray-900">
                <%= if assigns[:stats], do: @stats.active_games, else: 0 %>
              </p>
            </div>
          </div>
        </div>

        <!-- 活跃房间 -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span class="text-purple-600 text-lg">🏠</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">活跃房间</p>
              <p class="text-2xl font-semibold text-gray-900">
                <%= if assigns[:stats], do: @stats.active_rooms, else: 0 %>
              </p>
            </div>
          </div>
        </div>

        <!-- 实时充值 -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span class="text-yellow-600 text-lg">💰</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">实时充值</p>
              <p class="text-2xl font-semibold text-gray-900">
                ¥<%= if assigns[:stats] do
                  amount = Decimal.to_float(@stats.realtime_recharge) / 100
                  :erlang.float_to_binary(amount, decimals: 2)
                else
                  "0.00"
                end %>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 今日数据统计 -->
      <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">📈 今日数据</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 今日新增用户 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600">
                <%= if assigns[:stats], do: @stats.today_new_users, else: 0 %>
              </div>
              <div class="text-sm text-gray-500 mt-1">新增用户</div>
            </div>

            <!-- 今日活跃用户 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600">
                <%= if assigns[:stats], do: @stats.today_active_users, else: 0 %>
              </div>
              <div class="text-sm text-gray-500 mt-1">活跃用户</div>
            </div>

            <!-- 今日充值用户 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600">
                <%= if assigns[:stats], do: @stats.today_recharge_users, else: 0 %>
              </div>
              <div class="text-sm text-gray-500 mt-1">充值用户</div>
            </div>

            <!-- 今日充值金额 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-yellow-600">
                ¥<%= if assigns[:stats] do
                  amount = Decimal.to_float(@stats.today_recharge_amount) / 100
                  :erlang.float_to_binary(amount, decimals: 2)
                else
                  "0.00"
                end %>
              </div>
              <div class="text-sm text-gray-500 mt-1">充值金额</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据说明 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <span class="text-blue-400 text-lg">ℹ️</span>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">数据说明</h3>
            <div class="mt-2 text-sm text-blue-700">
              <ul class="list-disc list-inside space-y-1">
                <li>实时数据每30秒自动更新一次</li>
                <li>在线用户：当前在线的用户数量</li>
                <li>活跃游戏：正在进行的游戏数量</li>
                <li>活跃房间：有用户的房间数量</li>
                <li>今日数据：从00:00开始统计到当前时间</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
