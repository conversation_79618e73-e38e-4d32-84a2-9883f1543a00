defmodule Teen.ReportSystem do
  @moduledoc """
  报表系统域

  管理系统报表相关功能，包括：
  - 日报表生成和查询
  - 月报表生成和查询
  - 实时数据统计
  - 在线统计
  """

  use Ash.Domain

  resources do
    resource Teen.ReportSystem.DailyReport
    resource Teen.ReportSystem.MonthlyReport
    resource Teen.ReportSystem.RealtimeStats
  end

  # 报表生成相关函数
  def generate_daily_report(date, platform_id \\ nil) do
    Teen.ReportSystem.DailyReport.generate_daily_report(date, platform_id)
  end

  def generate_monthly_report(month, platform_id \\ nil) do
    Teen.ReportSystem.MonthlyReport.generate_monthly_report(month, platform_id)
  end

  def update_realtime_stats(platform_id \\ nil) do
    Teen.ReportSystem.RealtimeStats.update_realtime_stats(platform_id)
  end

  # 查询相关函数
  def get_daily_report(date, platform_id \\ nil) do
    case Teen.ReportSystem.DailyReport.get_by_date(date) do
      {:ok, reports} ->
        if platform_id do
          {:ok, Enum.filter(reports, fn report -> report.platform_id == platform_id end)}
        else
          {:ok, reports}
        end
      error -> error
    end
  end

  def get_monthly_report(month, platform_id \\ nil) do
    case Teen.ReportSystem.MonthlyReport.get_by_month(month) do
      {:ok, reports} ->
        if platform_id do
          {:ok, Enum.filter(reports, fn report -> report.platform_id == platform_id end)}
        else
          {:ok, reports}
        end
      error -> error
    end
  end

  def get_date_range_reports(start_date, end_date, platform_id \\ nil) do
    case Teen.ReportSystem.DailyReport.get_date_range(start_date, end_date) do
      {:ok, reports} ->
        if platform_id do
          {:ok, Enum.filter(reports, fn report -> report.platform_id == platform_id end)}
        else
          {:ok, reports}
        end
      error -> error
    end
  end

  def get_latest_realtime_stats(platform_id \\ nil) do
    case Teen.ReportSystem.RealtimeStats.get_latest_stats() do
      {:ok, stats} ->
        if platform_id do
          {:ok, Enum.filter(stats, fn stat -> stat.platform_id == platform_id end)}
        else
          {:ok, stats}
        end
      error -> error
    end
  end

  # 统计计算相关函数
  def calculate_retention_rate(new_users_date, check_date, days) do
    # 计算留存率的逻辑
    # 这里需要根据实际的用户数据来计算
    # 返回格式: {:ok, retention_rate} 或 {:error, reason}
    {:ok, Decimal.new("0")}
  end

  def calculate_arpu(total_revenue, total_users) do
    if total_users > 0 do
      Decimal.div(total_revenue, total_users)
    else
      Decimal.new("0")
    end
  end

  def calculate_arppu(total_revenue, paying_users) do
    if paying_users > 0 do
      Decimal.div(total_revenue, paying_users)
    else
      Decimal.new("0")
    end
  end

  def calculate_payment_rate(paying_users, total_users) do
    if total_users > 0 do
      Decimal.mult(Decimal.div(paying_users, total_users), 100)
    else
      Decimal.new("0")
    end
  end

  # 数据聚合相关函数
  def aggregate_daily_to_monthly(year, month, platform_id \\ nil) do
    start_date = Date.new!(year, month, 1)
    end_date = Date.end_of_month(start_date)
    
    case get_date_range_reports(start_date, end_date, platform_id) do
      {:ok, daily_reports} ->
        aggregate_data = %{
          report_month: "#{year}-#{String.pad_leading(to_string(month), 2, "0")}",
          platform_id: platform_id,
          total_new_devices: Enum.sum(Enum.map(daily_reports, & &1.new_devices)),
          total_new_registrations: Enum.sum(Enum.map(daily_reports, & &1.new_registrations)),
          total_effective_new_users: Enum.sum(Enum.map(daily_reports, & &1.effective_new_users)),
          total_mobile_registrations: Enum.sum(Enum.map(daily_reports, & &1.mobile_registrations)),
          total_active_users: Enum.sum(Enum.map(daily_reports, & &1.active_users)),
          total_effective_active_users: Enum.sum(Enum.map(daily_reports, & &1.effective_active_users)),
          total_recharge_users: Enum.sum(Enum.map(daily_reports, & &1.recharge_users)),
          total_recharge_amount: Enum.reduce(daily_reports, Decimal.new("0"), fn report, acc -> 
            Decimal.add(acc, report.recharge_amount) 
          end),
          total_withdrawal_amount: Enum.reduce(daily_reports, Decimal.new("0"), fn report, acc -> 
            Decimal.add(acc, report.withdrawal_amount) 
          end),
          avg_payment_rate: calculate_average_decimal(Enum.map(daily_reports, & &1.payment_rate)),
          avg_arpu: calculate_average_decimal(Enum.map(daily_reports, & &1.arpu)),
          avg_arppu: calculate_average_decimal(Enum.map(daily_reports, & &1.arppu)),
          avg_retention_day2: calculate_average_decimal(Enum.map(daily_reports, & &1.retention_day2)),
          avg_retention_day7: calculate_average_decimal(Enum.map(daily_reports, & &1.retention_day7)),
          avg_retention_day30: calculate_average_decimal(Enum.map(daily_reports, & &1.retention_day30))
        }
        
        Teen.ReportSystem.MonthlyReport.create(aggregate_data)
      error -> error
    end
  end

  # 辅助函数
  defp calculate_average_decimal(values) do
    if length(values) > 0 do
      sum = Enum.reduce(values, Decimal.new("0"), fn value, acc -> Decimal.add(acc, value) end)
      Decimal.div(sum, length(values))
    else
      Decimal.new("0")
    end
  end

  # 报表导出相关函数
  def export_daily_report_csv(start_date, end_date, platform_id \\ nil) do
    case get_date_range_reports(start_date, end_date, platform_id) do
      {:ok, reports} ->
        csv_data = generate_daily_report_csv(reports)
        {:ok, csv_data}
      error -> error
    end
  end

  def export_monthly_report_csv(year, platform_id \\ nil) do
    # 获取指定年份的所有月报表
    months = 1..12
    reports = Enum.flat_map(months, fn month ->
      month_str = "#{year}-#{String.pad_leading(to_string(month), 2, "0")}"
      case get_monthly_report(month_str, platform_id) do
        {:ok, [report]} -> [report]
        _ -> []
      end
    end)
    
    csv_data = generate_monthly_report_csv(reports)
    {:ok, csv_data}
  end

  defp generate_daily_report_csv(reports) do
    headers = [
      "日期", "平台ID", "新增设备", "新增注册", "有效新增", "手机注册绑定",
      "活跃用户", "有效活跃", "活跃手机绑定", "充值人数", "新增充值人数", "首付人数",
      "新增充值额", "退出总额", "退出人数", "退充比", "付费率", "新增付费率",
      "有效付费率", "有效新增付费率", "ARPU", "ARPPU", "有效ARPU",
      "次留", "3留", "7留", "14留", "30留"
    ]
    
    rows = Enum.map(reports, fn report ->
      [
        Date.to_string(report.report_date),
        report.platform_id || "全平台",
        report.new_devices,
        report.new_registrations,
        report.effective_new_users,
        report.mobile_registrations,
        report.active_users,
        report.effective_active_users,
        report.active_mobile_bindings,
        report.recharge_users,
        report.new_recharge_users,
        report.first_pay_users,
        Decimal.to_string(report.recharge_amount),
        Decimal.to_string(report.withdrawal_amount),
        report.withdrawal_users,
        Decimal.to_string(report.withdrawal_recharge_ratio),
        Decimal.to_string(report.payment_rate),
        Decimal.to_string(report.new_payment_rate),
        Decimal.to_string(report.effective_payment_rate),
        Decimal.to_string(report.effective_new_payment_rate),
        Decimal.to_string(report.arpu),
        Decimal.to_string(report.arppu),
        Decimal.to_string(report.effective_arpu),
        Decimal.to_string(report.retention_day2),
        Decimal.to_string(report.retention_day3),
        Decimal.to_string(report.retention_day7),
        Decimal.to_string(report.retention_day14),
        Decimal.to_string(report.retention_day30)
      ]
    end)
    
    [headers | rows]
    |> Enum.map(&Enum.join(&1, ","))
    |> Enum.join("\n")
  end

  defp generate_monthly_report_csv(reports) do
    headers = [
      "月份", "平台ID", "新增设备总数", "新增注册总数", "有效新增总数", "手机注册绑定总数",
      "活跃用户总数", "有效活跃总数", "充值用户总数", "充值总额", "提现总额",
      "平均付费率", "平均ARPU", "平均ARPPU", "平均次留", "平均7留", "平均30留"
    ]
    
    rows = Enum.map(reports, fn report ->
      [
        report.report_month,
        report.platform_id || "全平台",
        report.total_new_devices,
        report.total_new_registrations,
        report.total_effective_new_users,
        report.total_mobile_registrations,
        report.total_active_users,
        report.total_effective_active_users,
        report.total_recharge_users,
        Decimal.to_string(report.total_recharge_amount),
        Decimal.to_string(report.total_withdrawal_amount),
        Decimal.to_string(report.avg_payment_rate),
        Decimal.to_string(report.avg_arpu),
        Decimal.to_string(report.avg_arppu),
        Decimal.to_string(report.avg_retention_day2),
        Decimal.to_string(report.avg_retention_day7),
        Decimal.to_string(report.avg_retention_day30)
      ]
    end)
    
    [headers | rows]
    |> Enum.map(&Enum.join(&1, ","))
    |> Enum.join("\n")
  end
end
