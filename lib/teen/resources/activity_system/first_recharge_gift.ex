defmodule Teen.ActivitySystem.FirstRechargeGift do
  @moduledoc """
  首充礼包资源（Sale）

  管理新手福利首充礼包配置
  根据用户注册天数限制，提供首充奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :limit_days, :rewards, :status, :updated_at]
  end

  postgres do
    table "first_recharge_gifts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :get_by_user_days
    define :list_by_status
    define :list_by_limit_days
    define :list_active_and_enabled
    define :enable_gift
    define :disable_gift
    define :activate_gift
    define :deactivate_gift
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [
        :title,
        :limit_days,
        :min_recharge_amount,
        :rewards,
        :bonus_multiplier,
        :time_limit_hours,
        :description,
        :is_active,
        :status
      ]

      # 添加参数验证
      argument :validate_create_uniqueness, :boolean, default: true

      # 预处理：设置默认值和验证
      change Teen.ActivitySystem.FirstRechargeGift.Changes.CreateFirstRechargeGift
    end

    # 更新动作
    update :update do
      primary? true

      accept [
        :title,
        :limit_days,
        :min_recharge_amount,
        :rewards,
        :bonus_multiplier,
        :time_limit_hours,
        :description,
        :is_active,
        :status
      ]

      # 使用原子变更包装器进行唯一性验证
      change Teen.ActivitySystem.FirstRechargeGift.Changes.AtomicUpdateWrapper
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    read :list_active_gifts do
      filter expr(status == :enabled)
    end

    read :get_by_user_days do
      argument :user_days, :integer, allow_nil?: false
      filter expr(limit_days >= ^arg(:user_days) and status == :enabled)
    end

    read :list_by_status do
      argument :status, :atom, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_limit_days do
      argument :limit_days, :integer, allow_nil?: false
      filter expr(limit_days == ^arg(:limit_days) and status == :enabled)
    end

    read :list_active_and_enabled do
      filter expr(status == :enabled and is_active == true)
    end

    update :enable_gift do
      accept []
      change set_attribute(:status, :enabled)
    end

    update :disable_gift do
      accept []
      change set_attribute(:status, :disabled)
    end

    update :activate_gift do
      accept []
      change set_attribute(:is_active, true)
    end

    update :deactivate_gift do
      accept []
      change set_attribute(:is_active, false)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end



    attribute :limit_days, :integer do
      allow_nil? false
      public? true
      description "限制天数（注册天数）"
      constraints min: 1
      default 7
    end

    attribute :min_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "最小充值金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end

    attribute :bonus_multiplier, :decimal do
      allow_nil? false
      public? true
      description "奖励倍数"
      constraints min: Decimal.new("1")
      default Decimal.new("1")
    end

    attribute :time_limit_hours, :integer do
      allow_nil? true
      public? true
      description "时间限制（小时）"
      constraints min: 1
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述"
      constraints max_length: 500
    end

    attribute :is_active, :boolean do
      allow_nil? false
      public? true
      description "是否激活"
      default true
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :gift_description, :string do
      public? true
      description "礼包描述"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          min_amount_yuan = Decimal.div(record.min_recharge_amount, Decimal.new("100"))

          # 构建奖励描述
          reward_desc = Enum.map_join(record.rewards, "，", fn reward ->
            case reward.type do
              :coins -> "#{reward.amount}金币"
              :experience -> "#{reward.amount}经验"
              :items -> "#{reward.amount}道具"
              _ -> "#{reward.amount}奖励"
            end
          end)

          time_desc = if record.time_limit_hours do
            "，限时#{record.time_limit_hours}小时"
          else
            ""
          end

          "注册#{record.limit_days}天内首充#{min_amount_yuan}元获得#{reward_desc}#{time_desc}"
        end)
      end
    end

    calculate :total_reward_value, :integer do
      public? true
      description "总奖励价值（金币等价）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          base_value = Enum.reduce(record.rewards, 0, fn reward, acc ->
            case reward.type do
              :coins -> acc + reward.amount
              :experience -> acc + div(reward.amount, 10)  # 经验按1:10换算
              :items -> acc + reward.amount * 100  # 道具按1:100换算
              _ -> acc + reward.amount
            end
          end)

          # 应用奖励倍数
          Decimal.to_integer(Decimal.mult(Decimal.new(base_value), record.bonus_multiplier))
        end)
      end
    end

    calculate :is_time_limited, :boolean do
      public? true
      description "是否有时间限制"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          not is_nil(record.time_limit_hours)
        end)
      end
    end
  end

  identities do
    identity :unique_title, [:title]
  end
end
