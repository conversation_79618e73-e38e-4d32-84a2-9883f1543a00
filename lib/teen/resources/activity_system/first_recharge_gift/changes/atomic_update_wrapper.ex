defmodule Teen.ActivitySystem.FirstRechargeGift.Changes.AtomicUpdateWrapper do
  @moduledoc """
  原子变更包装器

  为首充礼包更新操作提供原子变更支持，包括：
  - 唯一性验证（确保标题不重复）
  - 数据完整性检查
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 对于非原子操作，执行唯一性验证
    title = Ash.Changeset.get_attribute(changeset, :title)
    current_data = changeset.data

    # 检查是否需要进行唯一性验证
    if should_validate_uniqueness?(current_data, title) do
      case check_title_uniqueness(title, current_data.id) do
        :ok ->
          changeset
        {:error, existing_gift} ->
          Ash.Changeset.add_error(changeset,
            field: :title,
            message: "标题\"#{title}\"的首充礼包已存在。现有礼包ID: #{existing_gift.id}。请选择其他标题或编辑现有礼包。"
          )
      end
    else
      changeset
    end
  end

  @impl true
  def atomic(changeset, _opts, _context) do
    # 获取变更的属性
    title = Ash.Changeset.get_attribute(changeset, :title)
    current_data = changeset.data

    # 如果 title 发生变化，需要进行唯一性检查
    if should_validate_uniqueness?(current_data, title) do
      # 构建原子条件：确保没有其他记录具有相同的 title
      atomic_title = title || current_data.title

      # 返回原子更新条件
      {:atomic, [
        # 确保更新时没有冲突的记录存在
        where: expr(
          not exists(
            from f in Teen.ActivitySystem.FirstRechargeGift,
            where: f.title == ^atomic_title and
                   f.id != parent_as(:self).id
          )
        )
      ]}
    else
      # 如果不需要唯一性验证，使用默认原子操作
      {:atomic, []}
    end
  end

  # 检查是否需要进行唯一性验证
  defp should_validate_uniqueness?(current_data, new_title) do
    # 如果标题发生了变化，则需要验证唯一性
    not is_nil(new_title) and new_title != current_data.title
  end

  # 检查标题唯一性（排除当前记录）
  defp check_title_uniqueness(title, current_id) do
    case Teen.ActivitySystem.FirstRechargeGift
         |> Ash.Query.filter(title == ^title and id != ^current_id)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_gift} -> {:error, existing_gift}
      {:error, _} -> :ok
    end
  end
end
