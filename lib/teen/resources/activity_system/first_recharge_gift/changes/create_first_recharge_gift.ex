defmodule Teen.ActivitySystem.FirstRechargeGift.Changes.CreateFirstRechargeGift do
  @moduledoc """
  创建首充礼包时的处理逻辑

  包括：
  - 唯一性验证（确保标题不重复）
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取标题
    title = Ash.Changeset.get_attribute(changeset, :title)

    # 检查唯一性（如果启用验证）
    validate_create_uniqueness = Ash.Changeset.get_argument(changeset, :validate_create_uniqueness)

    changeset = if validate_create_uniqueness != false and not is_nil(title) do
      case check_title_uniqueness(title) do
        :ok ->
          changeset
        {:error, existing_gift} ->
          Ash.Changeset.add_error(changeset,
            field: :title,
            message: "标题\"#{title}\"的首充礼包已存在。现有礼包ID: #{existing_gift.id}。请选择其他标题或编辑现有礼包。"
          )
      end
    else
      changeset
    end

    # 设置默认值
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
    |> Ash.Changeset.change_attribute(:is_active, true)
  end

  # 检查标题唯一性
  defp check_title_uniqueness(title) do
    case Teen.ActivitySystem.FirstRechargeGift
         |> Ash.Query.filter(title == ^title)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_gift} -> {:error, existing_gift}
      {:error, _} -> :ok
    end
  end
end
