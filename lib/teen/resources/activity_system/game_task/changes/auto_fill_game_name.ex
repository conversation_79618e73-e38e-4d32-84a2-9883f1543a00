defmodule Teen.ActivitySystem.GameTask.Changes.AutoFillGameName do
  @moduledoc """
  自动填充游戏名称的 Change 模块

  当游戏ID发生变化或游戏名称为空时，自动根据游戏ID填充游戏名称
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    current_game_name = Ash.Changeset.get_attribute(changeset, :game_name)

    # 如果游戏ID发生变化或游戏名称为空/默认值，则自动填充
    game_name =
      if is_integer(game_id) and game_id > 0 and
         (is_nil(current_game_name) or current_game_name == "未知游戏名称" or current_game_name == "") do
        get_game_name_by_id(game_id)
      else
        current_game_name || "未知游戏名称"
      end

    Ash.Changeset.change_attribute(changeset, :game_name, game_name)
  end



  # 根据游戏ID获取游戏名称
  defp get_game_name_by_id(game_id) when is_integer(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_id) do
        {:ok, game} -> game.display_name
        {:error, _} -> get_fallback_game_name(game_id)
      end
    rescue
      _error -> get_fallback_game_name(game_id)
    end
  end

  defp get_game_name_by_id(_), do: "未知游戏名称"

  # 备选游戏名称映射
  defp get_fallback_game_name(game_id) do
    case game_id do
      1 -> "Teen Patti"
      22 -> "Dragon Tiger"
      23 -> "Crash"
      30 -> "Andar Bahar"
      31 -> "Jhandi Munda"
      32 -> "AK47 Teen Patti"
      33 -> "Pot Blind"
      34 -> "Safari of Wealth"
      40 -> "Slot 777"
      41 -> "Slot Niu"
      42 -> "Slot Cat"
      _ -> "未知游戏 (ID: #{game_id})"
    end
  end
end
