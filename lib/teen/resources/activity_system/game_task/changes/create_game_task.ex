defmodule Teen.ActivitySystem.GameTask.Changes.CreateGameTask do
  @moduledoc """
  创建游戏任务时的处理逻辑

  包括：
  - 唯一性验证
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, opts, _context) do
    # 获取游戏ID和任务类型
    game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    task_type = Ash.Changeset.get_attribute(changeset, :task_type)

    # 检查唯一性（如果启用验证）
    validate_uniqueness = Ash.Changeset.get_argument(changeset, :validate_uniqueness)

    changeset = if validate_uniqueness != false and not is_nil(game_id) and not is_nil(task_type) do
      case check_game_task_uniqueness(game_id, task_type) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :game_id,
            message: "该游戏(ID: #{game_id})的#{get_task_type_display(task_type)}任务已存在。现有任务: \"#{existing_task.task_name}\"。请选择其他游戏或任务类型，或编辑现有任务。"
          )
      end
    else
      changeset
    end

    # 设置默认状态
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
  end



  # 检查游戏任务唯一性
  defp check_game_task_uniqueness(game_id, task_type) do
    case Teen.ActivitySystem.GameTask
         |> Ash.Query.filter(game_id == ^game_id and task_type == ^task_type)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end

  # 获取任务类型显示名称
  defp get_task_type_display(task_type) do
    case task_type do
      :game_rounds -> "游戏局数"
      :recharge_amount -> "充值金额"
      :win_rounds -> "游戏胜利"
      :wheel_spins -> "转盘次数"
      :task_completion -> "完成任务"
      _ -> "未知类型"
    end
  end


end
