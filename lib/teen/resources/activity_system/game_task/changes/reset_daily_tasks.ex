defmodule Teen.ActivitySystem.GameTask.Changes.ResetDailyTasks do
  @moduledoc """
  每日任务重置的 Change 模块

  处理每日任务重置的逻辑
  """

  use Ash.Resource.Change
  require Logger

  @impl true
  def change(changeset, _opts, _context) do
    case reset_all_daily_tasks() do
      {:ok, result} ->
        Logger.info("每日任务重置完成: #{inspect(result)}")
        changeset

      {:error, reason} ->
        Logger.error("每日任务重置失败: #{inspect(reason)}")

        changeset
        |> Ash.Changeset.add_error(field: :base, message: "每日任务重置失败: #{inspect(reason)}")
    end
  end

  # 私有函数 - 每日任务重置逻辑
  defp reset_all_daily_tasks do
    Logger.info("📝 开始重置每日任务...")

    # 这里可以实现具体的任务重置逻辑
    # 例如：重置用户任务进度、生成新的每日任务等

    # 示例：重置所有用户的每日任务进度
    # 实际实现需要根据具体的任务进度表结构来调整

    Logger.info("📝 每日任务重置完成")
    {:ok, %{message: "每日任务已重置", reset_time: DateTime.utc_now()}}
  end

  @impl true
  def atomic(changeset, _opts, context) do
    # 每日任务重置不支持原子操作，因为需要复杂的业务逻辑
    {:not_atomic, "Daily task reset requires complex business logic"}
  end

  # 私有函数 - 每日任务重置逻辑
  defp reset_all_daily_tasks do
    try do
      # 这里可以添加具体的重置逻辑
      # 例如：重置用户的每日任务进度、更新任务状态等

      Logger.info("📝 每日任务重置完成")
      {:ok, %{message: "每日任务已重置", reset_time: DateTime.utc_now()}}
    rescue
      error ->
        Logger.error("每日任务重置异常: #{inspect(error)}")
        {:error, error}
    end
  end
end
