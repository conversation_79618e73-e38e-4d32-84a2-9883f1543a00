defmodule Teen.ActivitySystem.GameTask.Changes.UpdateGameTask do
  @moduledoc """
  更新游戏任务时的处理逻辑

  包括：
  - 可选的唯一性验证（当游戏ID或任务类型发生变化时）
  - 自动填充游戏名称
  - 数据验证
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取当前数据和变更数据
    current_data = changeset.data
    new_game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    new_task_type = Ash.Changeset.get_attribute(changeset, :task_type)
    validate_uniqueness = Ash.Changeset.get_argument(changeset, :validate_uniqueness)
    skip_game_name_fill = Ash.Changeset.get_argument(changeset, :skip_game_name_fill)

    # 检查是否需要进行唯一性验证
    changeset = if validate_uniqueness and should_validate_uniqueness?(current_data, new_game_id, new_task_type) do
      case check_game_task_uniqueness(new_game_id, new_task_type, current_data.id) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :game_id,
            message: "该游戏(ID: #{new_game_id})的#{get_task_type_display(new_task_type)}任务已存在。现有任务: \"#{existing_task.task_name}\"。请选择其他游戏或任务类型，或编辑现有任务。"
          )
      end
    else
      changeset
    end

    changeset
  end



  # 检查是否需要进行唯一性验证
  defp should_validate_uniqueness?(current_data, new_game_id, new_task_type) do
    # 如果游戏ID或任务类型发生了变化，则需要验证唯一性
    (not is_nil(new_game_id) and new_game_id != current_data.game_id) or
    (not is_nil(new_task_type) and new_task_type != current_data.task_type)
  end

  # 检查游戏任务唯一性（排除当前记录）
  defp check_game_task_uniqueness(game_id, task_type, current_id) do
    case Teen.ActivitySystem.GameTask
         |> Ash.Query.filter(game_id == ^game_id and task_type == ^task_type and id != ^current_id)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end

  # 获取任务类型显示名称
  defp get_task_type_display(task_type) do
    case task_type do
      :game_rounds -> "游戏局数"
      :recharge_amount -> "充值金额"
      :win_rounds -> "游戏胜利"
      :wheel_spins -> "转盘次数"
      :task_completion -> "完成任务"
      _ -> "未知类型"
    end
  end

  # 根据游戏ID获取游戏名称
  defp get_game_name_by_id(game_id) when is_integer(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_id) do
        {:ok, game} -> game.display_name
        {:error, _} -> get_fallback_game_name(game_id)
      end
    rescue
      _error -> get_fallback_game_name(game_id)
    end
  end

  defp get_game_name_by_id(_), do: "未知游戏名称"

  # 备选游戏名称映射
  defp get_fallback_game_name(game_id) do
    case game_id do
      1 -> "Teen Patti"
      22 -> "Dragon Tiger"
      23 -> "Crash"
      30 -> "Andar Bahar"
      31 -> "Jhandi Munda"
      32 -> "AK47 Teen Patti"
      33 -> "Pot Blind"
      34 -> "Safari of Wealth"
      40 -> "Slot 777"
      41 -> "Slot Niu"
      42 -> "Slot Cat"
      _ -> "未知游戏 (ID: #{game_id})"
    end
  end
end
