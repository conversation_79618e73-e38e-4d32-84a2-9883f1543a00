defmodule Teen.ActivitySystem.GameTask do
  @moduledoc """
  游戏任务资源

  管理每日游戏任务配置，包括：
  - 游戏局数任务
  - 游戏赢的局数任务
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :task_name,
      :task_type,
      :required_count,
      :max_claims,
      :reward_amount,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "game_tasks"
    repo Cy<PERSON>ridina.Repo
  end

  relationships do
    belongs_to :game_config, Teen.GameManagement.ManageGameConfig do
      source_attribute :game_id
      destination_attribute :game_id
      attribute_writable? true  # 允许通过 game_id 更新关联
    end
  end
  # AshOban 配置 - 每日任务重置
  oban do
    domain Teen.ActivitySystem

    scheduled_actions do
      schedule :reset_daily_tasks, "@daily" do
        worker_module_name(Teen.ActivitySystem.GameTask.AshOban.ActionWorker.ResetDailyTasks)
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_game
    define :enable_task
    define :disable_task
    define :reset_daily_tasks
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [
        :task_name,
        :game_id,
        :task_type,
        :required_count,
        :max_claims,
        :target_value,
        :reward_amount,
        :reward_type,
        :status,
        :game_config_id,
        :is_active,
        :start_date,
        :end_date
      ]

      # 添加参数验证
      argument :validate_uniqueness, :boolean, default: true

      # 预处理：设置默认值和验证
      change Teen.ActivitySystem.GameTask.Changes.CreateGameTask
    end

    # 更新动作
    update :update do
      primary? true

      accept [
        :task_name,
        :game_id,
        :task_type,
        :required_count,
        :max_claims,
        :target_value,
        :reward_amount,
        :reward_type,
        :status,
        :game_config_id,
        :is_active,
        :start_date,
        :end_date
      ]

      # 添加参数验证
      argument :validate_uniqueness, :boolean, default: false

      # 自动填充游戏名称和唯一性验证
      change Teen.ActivitySystem.GameTask.Changes.UpdateGameTask
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    # 批量删除动作
    destroy :bulk_destroy do
      argument :ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:ids))
    end

    # 查询动作
    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_game do
      argument :game_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    read :list_by_status do
      argument :status, :atom, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_type do
      argument :task_type, :atom, allow_nil?: false
      filter expr(task_type == ^arg(:task_type))
    end

    # 状态管理动作
    update :enable_task do
      accept []
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      accept []
      change set_attribute(:status, :disabled)
    end

    update :activate_task do
      accept []
      change set_attribute(:is_active, true)
    end

    update :deactivate_task do
      accept []
      change set_attribute(:is_active, false)
    end

    # 批量状态更新
    update :bulk_enable do
      argument :ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:ids))
      change set_attribute(:status, :enabled)
    end

    update :bulk_disable do
      argument :ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:ids))
      change set_attribute(:status, :disabled)
    end

    # 每日任务重置的定时任务
    create :reset_daily_tasks do
      accept []

      change Teen.ActivitySystem.GameTask.Changes.ResetDailyTasks
    end
  end

  validations do
    validate compare(:required_count, greater_than: 0), message: "所需局数必须大于0"
    validate compare(:max_claims, greater_than: 0), message: "最大领取次数必须大于0"

    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "奖励金额不能为负数"

    # 验证必填字段
    validate present([:task_name, :game_id, :task_type]), message: "任务名称、游戏ID和任务类型为必填项"
  end

  attributes do
    uuid_primary_key :id

    attribute :task_name, :string do
      allow_nil? false
      public? true
      description "任务名称"
      constraints max_length: 100
      default "新游戏任务"
    end

    attribute :game_id, :integer do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints min: 1
      default 1
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      default :game_rounds

      constraints one_of: [
                    :game_rounds,
                    :recharge_amount,
                    :win_rounds,
                    :wheel_spins,
                    :task_completion
                  ]
    end

    attribute :required_count, :integer do
      allow_nil? false
      public? true
      description "所需局数"
      constraints min: 1
      default 1
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "每日最大领取次数"
      constraints min: 1
      default 1
    end

    attribute :target_value, :integer do
      allow_nil? true
      public? true
      description "目标值"
      constraints min: 1
      default 1
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? true
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :items]
      default :coins
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :game_config_id, :uuid do
      allow_nil? true
      public? true
      description "关联的游戏配置ID"
    end

    attribute :is_active, :boolean do
      allow_nil? true
      public? true
      description "是否激活"
      default true
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
      default &Date.utc_today/0
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
      default fn -> Date.add(Date.utc_today(), 30) end
    end

    # attribute :inserted_at, :utc_datetime_usec do
    #   allow_nil? false
    #   public? true
    #   description "创建时间"
    #   writable? false
    #   default &DateTime.utc_now/0
    # end

    # attribute :updated_at, :utc_datetime_usec do
    #   allow_nil? false
    #   public? true
    #   description "更新时间"
    #   writable? false
    #   default &DateTime.utc_now/0
    #   update_default &DateTime.utc_now/0
    # end
    timestamps()
  end

  calculations do
    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :game_rounds -> "游戏局数"
            :recharge_amount -> "充值金额"
            :win_rounds -> "游戏赢的局数"
            :wheel_spins -> "转盘次数"
            :task_completion -> "完成任务"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_game_task, [:game_id, :task_type]
  end


end
