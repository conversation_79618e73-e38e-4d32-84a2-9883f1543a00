defmodule Teen.ActivitySystem.RechargeTask do
  @moduledoc """
  充值任务资源

  管理充值奖励配置
  规则：充值X金额奖励Y金币
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :recharge_amount, :rewards, :reward_rate, :status, :updated_at]
  end

  postgres do
    table "recharge_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_amount_range
    define :enable_task
    define :disable_task
    define :get_reward_for_amount
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [:recharge_amount, :rewards, :status]

      # 添加参数验证
      argument :validate_create_uniqueness, :boolean, default: true

      # 预处理：设置默认值和验证
      change Teen.ActivitySystem.RechargeTask.Changes.CreateRechargeTask
    end

    # 更新动作
    update :update do
      primary? true

      accept [:recharge_amount, :rewards, :status]

      # 使用原子变更包装器进行唯一性验证
      change Teen.ActivitySystem.RechargeTask.Changes.AtomicUpdateWrapper
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_amount_range do
      argument :min_amount, :decimal, allow_nil?: false
      argument :max_amount, :decimal, allow_nil?: false

      filter expr(
               recharge_amount >= ^arg(:min_amount) and recharge_amount <= ^arg(:max_amount) and
                 status == :enabled
             )
    end

    read :get_reward_for_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_task do
      accept []
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      accept []
      change set_attribute(:status, :disabled)
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"

    # 验证必填字段
    validate present([:recharge_amount, :rewards]), message: "充值金额和奖励配置为必填项"
  end

  attributes do
    uuid_primary_key :id

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :reward_rate, :decimal do
      public? true
      description "奖励比例（奖励金额/充值金额）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.recharge_amount, Decimal.new("0")) == :gt do
            # 计算总奖励金额
            total_reward = Enum.reduce(record.rewards, Decimal.new("0"), fn reward, acc ->
              Decimal.add(acc, Decimal.new(reward.amount))
            end)

            Decimal.div(total_reward, record.recharge_amount)
            |> Decimal.mult(Decimal.new("100"))
            |> Decimal.round(2)
          else
            Decimal.new("0")
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_description, :string do
      public? true
      description "任务描述"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          recharge_yuan = Decimal.div(record.recharge_amount, Decimal.new("100"))

          # 构建奖励描述
          reward_desc = Enum.map_join(record.rewards, "，", fn reward ->
            case reward.type do
              :coins -> "#{reward.amount}金币"
              :experience -> "#{reward.amount}经验"
              :items -> "#{reward.amount}道具"
              _ -> "#{reward.amount}奖励"
            end
          end)

          "充值#{recharge_yuan}元奖励#{reward_desc}"
        end)
      end
    end
  end

  identities do
    identity :unique_recharge_amount, [:recharge_amount]
  end
end
