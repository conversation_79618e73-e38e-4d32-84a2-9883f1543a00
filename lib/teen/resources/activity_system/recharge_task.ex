defmodule Teen.ActivitySystem.RechargeTask do
  @moduledoc """
  充值任务资源

  管理充值奖励配置
  规则：充值X金额奖励Y金币
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :recharge_amount, :recharge_type, :rewards, :reward_rate, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "recharge_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_amount_range
    define :enable_task
    define :disable_task
    define :get_reward_for_amount
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [
        :recharge_amount,
        :recharge_type,
        :rewards,
        :status,
        :start_date,
        :end_date
      ]

      # 添加参数验证
      argument :validate_create_uniqueness, :boolean, default: true

      # 预处理：设置默认值和验证
      change Teen.ActivitySystem.RechargeTask.Changes.CreateRechargeTask
    end

    # 更新动作
    update :update do
      primary? true

      accept [
        :recharge_amount,
        :recharge_type,
        :rewards,
        :status,
        :start_date,
        :end_date
      ]

      # 使用原子变更包装器进行唯一性验证
      change Teen.ActivitySystem.RechargeTask.Changes.AtomicUpdateWrapper
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    read :list_active_tasks do
      filter expr(status == :enabled and start_date <= ^Date.utc_today() and end_date >= ^Date.utc_today())
    end

    read :list_current_tasks do
      filter expr(
        status == :enabled and
        start_date <= ^Date.utc_today() and
        end_date >= ^Date.utc_today()
      )
    end

    read :list_by_amount_range do
      argument :min_amount, :decimal, allow_nil?: false
      argument :max_amount, :decimal, allow_nil?: false

      filter expr(
               recharge_amount >= ^arg(:min_amount) and recharge_amount <= ^arg(:max_amount) and
                 status == :enabled
             )
    end

    read :get_reward_for_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_task do
      accept []
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      accept []
      change set_attribute(:status, :disabled)
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"

    # 验证必填字段
    validate present([:recharge_amount, :rewards, :start_date, :end_date]), message: "充值金额、奖励配置、开始日期和结束日期为必填项"

    # 验证日期范围
    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      if start_date && end_date && Date.compare(start_date, end_date) == :gt do
        {:error, field: :end_date, message: "结束日期必须晚于开始日期"}
      else
        :ok
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额条件（分）"
      constraints min: Decimal.new("0")
    end

    attribute :recharge_type, :atom do
      allow_nil? false
      public? true
      description "充值条件类型"
      constraints one_of: [:single_recharge, :cumulative_recharge, :first_recharge, :daily_recharge]
      default :single_recharge
    end

    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end

    attribute :start_date, :date do
      allow_nil? false
      public? true
      description "活动开始日期"
      default &Date.utc_today/0
    end

    attribute :end_date, :date do
      allow_nil? false
      public? true
      description "活动结束日期"
      default fn -> Date.add(Date.utc_today(), 30) end
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :reward_rate, :decimal do
      public? true
      description "奖励比例（奖励金额/充值金额）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.recharge_amount, Decimal.new("0")) == :gt do
            # 计算总奖励金额
            total_reward = Enum.reduce(record.rewards, Decimal.new("0"), fn reward, acc ->
              Decimal.add(acc, Decimal.new(reward.amount))
            end)

            Decimal.div(total_reward, record.recharge_amount)
            |> Decimal.mult(Decimal.new("100"))
            |> Decimal.round(2)
          else
            Decimal.new("0")
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_description, :string do
      public? true
      description "任务描述"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          recharge_yuan = Decimal.div(record.recharge_amount, Decimal.new("100"))

          # 构建充值条件描述
          condition_desc = case record.recharge_type do
            :single_recharge -> "单次充值"
            :cumulative_recharge -> "累计充值"
            :first_recharge -> "首次充值"
            :daily_recharge -> "每日充值"
            _ -> "充值"
          end

          # 构建奖励描述
          reward_desc = Enum.map_join(record.rewards, "，", fn reward ->
            case reward.type do
              :coins -> "#{reward.amount}金币"
              :experience -> "#{reward.amount}经验"
              :items -> "#{reward.amount}道具"
              _ -> "#{reward.amount}奖励"
            end
          end)

          "#{condition_desc}#{recharge_yuan}元奖励#{reward_desc}"
        end)
      end
    end

    calculate :recharge_type_display, :string do
      public? true
      description "充值条件显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.recharge_type do
            :single_recharge -> "单次充值"
            :cumulative_recharge -> "累计充值"
            :first_recharge -> "首次充值"
            :daily_recharge -> "每日充值"
            _ -> "未知条件"
          end
        end)
      end
    end

    calculate :is_active, :boolean do
      public? true
      description "是否在活动期间"

      calculation fn records, _context ->
        today = Date.utc_today()
        Enum.map(records, fn record ->
          record.status == :enabled and
          Date.compare(record.start_date, today) != :gt and
          Date.compare(record.end_date, today) != :lt
        end)
      end
    end

    calculate :days_remaining, :integer do
      public? true
      description "剩余天数"

      calculation fn records, _context ->
        today = Date.utc_today()
        Enum.map(records, fn record ->
          if Date.compare(record.end_date, today) == :gt do
            Date.diff(record.end_date, today)
          else
            0
          end
        end)
      end
    end
  end

  identities do
    identity :unique_recharge_amount, [:recharge_amount]
  end
end
