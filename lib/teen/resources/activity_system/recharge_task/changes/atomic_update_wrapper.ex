defmodule Teen.ActivitySystem.RechargeTask.Changes.AtomicUpdateWrapper do
  @moduledoc """
  原子变更包装器

  为充值任务更新操作提供原子变更支持，包括：
  - 唯一性验证（确保充值金额不重复）
  - 数据完整性检查
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 对于非原子操作，执行唯一性验证
    recharge_amount = Ash.Changeset.get_attribute(changeset, :recharge_amount)
    current_data = changeset.data

    # 检查是否需要进行唯一性验证
    if should_validate_uniqueness?(current_data, recharge_amount) do
      case check_recharge_amount_uniqueness(recharge_amount, current_data.id) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :recharge_amount,
            message: "充值金额#{recharge_amount}的任务已存在。现有任务ID: #{existing_task.id}。请选择其他充值金额或编辑现有任务。"
          )
      end
    else
      changeset
    end
  end

  @impl true
  def atomic(changeset, _opts, _context) do
    # 获取变更的属性
    recharge_amount = Ash.Changeset.get_attribute(changeset, :recharge_amount)
    current_data = changeset.data

    # 如果 recharge_amount 发生变化，需要进行唯一性检查
    if should_validate_uniqueness?(current_data, recharge_amount) do
      # 构建原子条件：确保没有其他记录具有相同的 recharge_amount
      atomic_recharge_amount = recharge_amount || current_data.recharge_amount

      # 返回原子更新条件
      {:atomic, [
        # 确保更新时没有冲突的记录存在
        where: expr(
          not exists(
            from r in Teen.ActivitySystem.RechargeTask,
            where: r.recharge_amount == ^atomic_recharge_amount and
                   r.id != parent_as(:self).id
          )
        )
      ]}
    else
      # 如果不需要唯一性验证，使用默认原子操作
      {:atomic, []}
    end
  end

  # 检查是否需要进行唯一性验证
  defp should_validate_uniqueness?(current_data, new_recharge_amount) do
    # 如果充值金额发生了变化，则需要验证唯一性
    not is_nil(new_recharge_amount) and 
    Decimal.compare(new_recharge_amount, current_data.recharge_amount) != :eq
  end

  # 检查充值金额唯一性（排除当前记录）
  defp check_recharge_amount_uniqueness(recharge_amount, current_id) do
    case Teen.ActivitySystem.RechargeTask
         |> Ash.Query.filter(recharge_amount == ^recharge_amount and id != ^current_id)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end
end
