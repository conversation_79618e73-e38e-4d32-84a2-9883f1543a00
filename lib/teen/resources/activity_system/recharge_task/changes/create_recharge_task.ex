defmodule Teen.ActivitySystem.RechargeTask.Changes.CreateRechargeTask do
  @moduledoc """
  创建充值任务时的处理逻辑

  包括：
  - 唯一性验证（确保充值金额不重复）
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取充值金额
    recharge_amount = Ash.Changeset.get_attribute(changeset, :recharge_amount)

    # 检查唯一性（如果启用验证）
    validate_create_uniqueness = Ash.Changeset.get_argument(changeset, :validate_create_uniqueness)

    changeset = if validate_create_uniqueness != false and not is_nil(recharge_amount) do
      case check_recharge_amount_uniqueness(recharge_amount) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :recharge_amount,
            message: "充值金额#{recharge_amount}的任务已存在。现有任务ID: #{existing_task.id}。请选择其他充值金额或编辑现有任务。"
          )
      end
    else
      changeset
    end

    # 设置默认状态
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
  end

  # 检查充值金额唯一性
  defp check_recharge_amount_uniqueness(recharge_amount) do
    case Teen.ActivitySystem.RechargeTask
         |> Ash.Query.filter(recharge_amount == ^recharge_amount)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end
end
