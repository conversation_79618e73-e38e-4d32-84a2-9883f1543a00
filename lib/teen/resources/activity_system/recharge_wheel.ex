defmodule Teen.ActivitySystem.RechargeWheel do
  @moduledoc """
  充值转盘资源

  管理充值转盘活动配置，包括：
  - 累计充值金额配置
  - 转盘次数奖励
  - 活动状态管理
  转盘奖金池系统：用户累计充值达到指定金额后可获得转盘次数
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :wheel_name,
      :cumulative_recharge,
      :wheel_spins,
      :rewards,
      :status,
      :is_active,
      :updated_at
    ]
  end

  postgres do
    table "recharge_wheels"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy, action: :destroy
    define :list_active_wheels
    define :list_by_status
    define :get_by_recharge_amount, args: [:recharge_amount]
    define :enable_wheel
    define :disable_wheel
    define :activate_wheel
    define :deactivate_wheel
    define :bulk_enable
    define :bulk_disable
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [
        :wheel_name,
        :cumulative_recharge,
        :wheel_spins,
        :rewards,
        :status,
        :is_active,
        :start_date,
        :end_date,
        :description
      ]

      # 添加参数验证
      argument :validate_create_uniqueness, :boolean, default: true

      # 预处理：设置默认值
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:is_active, true)
      end
    end

    # 更新动作
    update :update do
      primary? true

      accept [
        :wheel_name,
        :cumulative_recharge,
        :wheel_spins,
        :rewards,
        :status,
        :is_active,
        :start_date,
        :end_date,
        :description
      ]
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    # 查询动作
    read :list_active_wheels do
      filter expr(status == :enabled and is_active == true)
    end

    read :list_by_status do
      argument :status, :atom, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :get_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(cumulative_recharge <= ^arg(:recharge_amount) and status == :enabled and is_active == true)
    end

    # 状态管理动作
    update :enable_wheel do
      accept []
      change set_attribute(:status, :enabled)
    end

    update :disable_wheel do
      accept []
      change set_attribute(:status, :disabled)
    end

    update :activate_wheel do
      accept []
      change set_attribute(:is_active, true)
    end

    update :deactivate_wheel do
      accept []
      change set_attribute(:is_active, false)
    end

    # 批量状态更新
    update :bulk_enable do
      argument :ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:ids))
      change set_attribute(:status, :enabled)
    end

    update :bulk_disable do
      argument :ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:ids))
      change set_attribute(:status, :disabled)
    end
  end

  validations do
    validate compare(:cumulative_recharge, greater_than: Decimal.new("0")), message: "累计充值金额必须大于0"
    validate compare(:wheel_spins, greater_than: 0), message: "转盘次数必须大于0"
    validate compare(:start_date, less_than_or_equal_to: :end_date), message: "开始日期不能晚于结束日期"

    # 验证必填字段
    validate present([:wheel_name, :cumulative_recharge, :wheel_spins]), message: "转盘名称、累计充值金额和转盘次数为必填项"
  end

  attributes do
    uuid_primary_key :id

    attribute :wheel_name, :string do
      allow_nil? false
      public? true
      description "转盘名称"
      constraints max_length: 100
      default "充值转盘"
    end

    attribute :cumulative_recharge, :decimal do
      allow_nil? false
      public? true
      description "累计充值金币（分）"
      constraints min: Decimal.new("0")
    end

    attribute :wheel_spins, :integer do
      allow_nil? false
      public? true
      description "转盘次数"
      constraints min: 1
      default 1
    end

    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :is_active, :boolean do
      allow_nil? true
      public? true
      description "是否激活"
      default true
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
      default &Date.utc_today/0
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
      default fn -> Date.add(Date.utc_today(), 30) end
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    timestamps()
  end

  calculations do
    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :recharge_amount_display, :string do
      public? true
      description "充值金额显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          amount = Decimal.to_float(record.cumulative_recharge) / 100
          "¥#{:erlang.float_to_binary(amount, decimals: 2)}"
        end)
      end
    end

    calculate :reward_summary, :string do
      public? true
      description "奖励摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if is_list(record.rewards) and length(record.rewards) > 0 do
            total_amount = Enum.reduce(record.rewards, 0, fn reward, acc -> acc + reward.amount end)
            "#{record.wheel_spins}次转盘/奖励#{total_amount}"
          else
            "#{record.wheel_spins}次转盘"
          end
        end)
      end
    end

    calculate :activity_period, :string do
      public? true
      description "活动期间"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.start_date && record.end_date do
            "#{Date.to_string(record.start_date)} ~ #{Date.to_string(record.end_date)}"
          else
            "长期有效"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_recharge_amount, [:cumulative_recharge]
    identity :unique_wheel_name, [:wheel_name]
  end
end
