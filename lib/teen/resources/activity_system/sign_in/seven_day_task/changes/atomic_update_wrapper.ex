defmodule Teen.ActivitySystem.SevenDayTask.Changes.AtomicUpdateWrapper do
  @moduledoc """
  原子变更包装器

  为七日任务更新操作提供原子变更支持，包括：
  - 唯一性验证（确保天数不重复）
  - 数据完整性检查
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 对于非原子操作，执行唯一性验证
    day_number = Ash.Changeset.get_attribute(changeset, :day_number)
    current_data = changeset.data

    # 检查是否需要进行唯一性验证
    if should_validate_uniqueness?(current_data, day_number) do
      case check_day_uniqueness(day_number, current_data.id) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :day_number,
            message: "第#{day_number}天的任务已存在。现有任务ID: #{existing_task.id}。请选择其他天数或编辑现有任务。"
          )
      end
    else
      changeset
    end
  end

  @impl true
  def atomic(changeset, _opts, _context) do
    # 获取变更的属性
    day_number = Ash.Changeset.get_attribute(changeset, :day_number)
    current_data = changeset.data

    # 如果 day_number 发生变化，需要进行唯一性检查
    if should_validate_uniqueness?(current_data, day_number) do
      # 构建原子条件：确保没有其他记录具有相同的 day_number
      atomic_day_number = day_number || current_data.day_number

      # 返回原子更新条件
      {:atomic, [
        # 确保更新时没有冲突的记录存在
        where: expr(
          not exists(
            from t in Teen.ActivitySystem.SevenDayTask,
            where: t.day_number == ^atomic_day_number and
                   t.id != parent_as(:self).id
          )
        )
      ]}
    else
      # 如果不需要唯一性验证，使用默认原子操作
      {:atomic, []}
    end
  end

  # 检查是否需要进行唯一性验证
  defp should_validate_uniqueness?(current_data, new_day_number) do
    # 如果天数发生了变化，则需要验证唯一性
    not is_nil(new_day_number) and new_day_number != current_data.day_number
  end

  # 检查天数唯一性（排除当前记录）
  defp check_day_uniqueness(day_number, current_id) do
    case Teen.ActivitySystem.SevenDayTask
         |> Ash.Query.filter(day_number == ^day_number and id != ^current_id)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end
end
