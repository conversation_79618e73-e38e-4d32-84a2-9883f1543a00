defmodule Teen.ActivitySystem.SevenDayTask.Changes.CreateSevenDayTask do
  @moduledoc """
  创建七日任务时的处理逻辑

  包括：
  - 唯一性验证（确保每天只有一个任务）
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取天数
    day_number = Ash.Changeset.get_attribute(changeset, :day_number)

    # 检查唯一性（如果启用验证）
    validate_uniqueness = Ash.Changeset.get_argument(changeset, :validate_uniqueness)

    changeset = if validate_uniqueness != false and not is_nil(day_number) do
      case check_day_uniqueness(day_number) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :day_number,
            message: "第#{day_number}天的任务已存在。现有任务ID: #{existing_task.id}。请选择其他天数或编辑现有任务。"
          )
      end
    else
      changeset
    end

    # 设置默认状态
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
    |> Ash.Changeset.change_attribute(:is_cyclic, true)
  end

  # 检查天数唯一性
  defp check_day_uniqueness(day_number) do
    case Teen.ActivitySystem.SevenDayTask
         |> Ash.Query.filter(day_number == ^day_number)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end
end
