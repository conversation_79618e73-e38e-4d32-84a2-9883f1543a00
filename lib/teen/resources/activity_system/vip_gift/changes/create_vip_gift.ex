defmodule Teen.ActivitySystem.VipGift.Changes.CreateVipGift do
  @moduledoc """
  创建VIP礼包时的处理逻辑

  包括：
  - 唯一性验证（确保VIP等级不重复）
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取VIP等级
    vip_level = Ash.Changeset.get_attribute(changeset, :vip_level)

    # 检查唯一性（如果启用验证）
    validate_create_uniqueness = Ash.Changeset.get_argument(changeset, :validate_create_uniqueness)

    changeset = if validate_create_uniqueness != false and not is_nil(vip_level) do
      case check_vip_level_uniqueness(vip_level) do
        :ok ->
          changeset
        {:error, existing_gift} ->
          Ash.Changeset.add_error(changeset,
            field: :vip_level,
            message: "VIP等级#{vip_level}的礼包已存在。现有礼包ID: #{existing_gift.id}。请选择其他VIP等级或编辑现有礼包。"
          )
      end
    else
      changeset
    end

    # 设置默认状态
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
  end

  # 检查VIP等级唯一性
  defp check_vip_level_uniqueness(vip_level) do
    case Teen.ActivitySystem.VipGift
         |> Ash.Query.filter(vip_level == ^vip_level)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_gift} -> {:error, existing_gift}
      {:error, _} -> :ok
    end
  end
end
