defmodule Teen.ActivitySystem.WeeklyCard.Changes.CreateWeeklyCard do
  @moduledoc """
  创建周卡时的处理逻辑

  包括：
  - 唯一性验证（确保充值金额不重复）
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 获取充值金额
    recharge_amount = Ash.Changeset.get_attribute(changeset, :recharge_amount)

    # 检查唯一性（如果启用验证）
    validate_create_uniqueness = Ash.Changeset.get_argument(changeset, :validate_create_uniqueness)

    changeset = if validate_create_uniqueness != false and not is_nil(recharge_amount) do
      case check_recharge_amount_uniqueness(recharge_amount) do
        :ok ->
          changeset
        {:error, existing_card} ->
          Ash.Changeset.add_error(changeset,
            field: :recharge_amount,
            message: "充值金额#{recharge_amount}的周卡已存在。现有周卡: \"#{existing_card.title}\"。请选择其他充值金额或编辑现有周卡。"
          )
      end
    else
      changeset
    end

    # 设置默认状态
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
    |> Ash.Changeset.change_attribute(:claim_days, 7)
  end

  # 检查充值金额唯一性
  defp check_recharge_amount_uniqueness(recharge_amount) do
    case Teen.ActivitySystem.WeeklyCard
         |> Ash.Query.filter(recharge_amount == ^recharge_amount)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_card} -> {:error, existing_card}
      {:error, _} -> :ok
    end
  end
end
