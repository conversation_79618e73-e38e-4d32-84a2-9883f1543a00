defmodule Teen.ReportSystem.DailyReport do
  @moduledoc """
  系统日报表资源

  管理系统每日统计数据，包括：
  - 用户相关指标：新增设备、新增注册、有效新增、活跃用户等
  - 充值相关指标：充值人数、充值金额、付费率等
  - 留存相关指标：次留、3留、7留、14留、30留
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ReportSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :report_date,
      :platform_id,
      :new_devices,
      :new_registrations,
      :effective_new_users,
      :active_users,
      :recharge_users,
      :recharge_amount,
      :updated_at
    ]
  end

  postgres do
    table "daily_reports"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_by_date, args: [:report_date]
    define :get_by_platform, args: [:platform_id]
    define :get_date_range, args: [:start_date, :end_date]
    define :generate_daily_report, args: [:report_date, :platform_id]
  end

  actions do
    defaults [:read]

    # 创建动作
    create :create do
      primary? true

      accept [
        :report_date,
        :platform_id,
        :new_devices,
        :new_registrations,
        :effective_new_users,
        :mobile_registrations,
        :active_users,
        :effective_active_users,
        :active_mobile_bindings,
        :recharge_users,
        :new_recharge_users,
        :first_pay_users,
        :recharge_amount,
        :withdrawal_amount,
        :withdrawal_users,
        :withdrawal_recharge_ratio,
        :payment_rate,
        :new_payment_rate,
        :effective_payment_rate,
        :effective_new_payment_rate,
        :arpu,
        :arppu,
        :effective_arpu,
        :retention_day2,
        :retention_day3,
        :retention_day7,
        :retention_day14,
        :retention_day30
      ]
    end

    # 更新动作
    update :update do
      primary? true

      accept [
        :new_devices,
        :new_registrations,
        :effective_new_users,
        :mobile_registrations,
        :active_users,
        :effective_active_users,
        :active_mobile_bindings,
        :recharge_users,
        :new_recharge_users,
        :first_pay_users,
        :recharge_amount,
        :withdrawal_amount,
        :withdrawal_users,
        :withdrawal_recharge_ratio,
        :payment_rate,
        :new_payment_rate,
        :effective_payment_rate,
        :effective_new_payment_rate,
        :arpu,
        :arppu,
        :effective_arpu,
        :retention_day2,
        :retention_day3,
        :retention_day7,
        :retention_day14,
        :retention_day30
      ]
    end

    # 删除动作
    destroy :destroy do
      primary? true
    end

    # 查询动作
    read :get_by_date do
      argument :report_date, :date, allow_nil?: false
      filter expr(report_date == ^arg(:report_date))
    end

    read :get_by_platform do
      argument :platform_id, :uuid, allow_nil?: false
      filter expr(platform_id == ^arg(:platform_id))
    end

    read :get_date_range do
      argument :start_date, :date, allow_nil?: false
      argument :end_date, :date, allow_nil?: false
      filter expr(report_date >= ^arg(:start_date) and report_date <= ^arg(:end_date))
    end

    # 生成日报表
    create :generate_daily_report do
      argument :report_date, :date, allow_nil?: false
      argument :platform_id, :uuid, allow_nil?: true

      change Teen.ReportSystem.DailyReport.Changes.GenerateDailyReport
    end
  end

  validations do
    validate present([:report_date]), message: "报表日期为必填项"
    validate compare(:new_devices, greater_than_or_equal_to: 0), message: "新增设备数不能为负数"
    validate compare(:new_registrations, greater_than_or_equal_to: 0), message: "新增注册数不能为负数"
    validate compare(:recharge_amount, greater_than_or_equal_to: Decimal.new("0")), message: "充值金额不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :report_date, :date do
      allow_nil? false
      public? true
      description "报表日期"
    end

    attribute :platform_id, :uuid do
      allow_nil? true
      public? true
      description "平台ID（为空表示全平台统计）"
    end

    # 用户相关指标
    attribute :new_devices, :integer do
      allow_nil? false
      public? true
      description "新增设备数"
      default 0
    end

    attribute :new_registrations, :integer do
      allow_nil? false
      public? true
      description "新增注册数"
      default 0
    end

    attribute :effective_new_users, :integer do
      allow_nil? false
      public? true
      description "有效新增用户数"
      default 0
    end

    attribute :mobile_registrations, :integer do
      allow_nil? false
      public? true
      description "手机注册绑定数"
      default 0
    end

    attribute :active_users, :integer do
      allow_nil? false
      public? true
      description "活跃用户数"
      default 0
    end

    attribute :effective_active_users, :integer do
      allow_nil? false
      public? true
      description "有效活跃用户数"
      default 0
    end

    attribute :active_mobile_bindings, :integer do
      allow_nil? false
      public? true
      description "活跃手机绑定数"
      default 0
    end

    # 充值相关指标
    attribute :recharge_users, :integer do
      allow_nil? false
      public? true
      description "充值人数"
      default 0
    end

    attribute :new_recharge_users, :integer do
      allow_nil? false
      public? true
      description "新增充值人数"
      default 0
    end

    attribute :first_pay_users, :integer do
      allow_nil? false
      public? true
      description "首付人数"
      default 0
    end

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "新增充值额（分）"
      default Decimal.new("0")
    end

    attribute :withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "退出总额（分）"
      default Decimal.new("0")
    end

    attribute :withdrawal_users, :integer do
      allow_nil? false
      public? true
      description "退出人数"
      default 0
    end

    # 比率相关指标
    attribute :withdrawal_recharge_ratio, :decimal do
      allow_nil? false
      public? true
      description "退充比（%）"
      default Decimal.new("0")
    end

    attribute :payment_rate, :decimal do
      allow_nil? false
      public? true
      description "付费率（%）"
      default Decimal.new("0")
    end

    attribute :new_payment_rate, :decimal do
      allow_nil? false
      public? true
      description "新增付费率（%）"
      default Decimal.new("0")
    end

    attribute :effective_payment_rate, :decimal do
      allow_nil? false
      public? true
      description "有效付费率（%）"
      default Decimal.new("0")
    end

    attribute :effective_new_payment_rate, :decimal do
      allow_nil? false
      public? true
      description "有效新增付费率（%）"
      default Decimal.new("0")
    end

    # ARPU相关指标
    attribute :arpu, :decimal do
      allow_nil? false
      public? true
      description "ARPU（平均每用户收入）"
      default Decimal.new("0")
    end

    attribute :arppu, :decimal do
      allow_nil? false
      public? true
      description "ARPPU（平均每付费用户收入）"
      default Decimal.new("0")
    end

    attribute :effective_arpu, :decimal do
      allow_nil? false
      public? true
      description "有效ARPU"
      default Decimal.new("0")
    end

    # 留存相关指标
    attribute :retention_day2, :decimal do
      allow_nil? false
      public? true
      description "次日留存率（%）"
      default Decimal.new("0")
    end

    attribute :retention_day3, :decimal do
      allow_nil? false
      public? true
      description "3日留存率（%）"
      default Decimal.new("0")
    end

    attribute :retention_day7, :decimal do
      allow_nil? false
      public? true
      description "7日留存率（%）"
      default Decimal.new("0")
    end

    attribute :retention_day14, :decimal do
      allow_nil? false
      public? true
      description "14日留存率（%）"
      default Decimal.new("0")
    end

    attribute :retention_day30, :decimal do
      allow_nil? false
      public? true
      description "30日留存率（%）"
      default Decimal.new("0")
    end

    timestamps()
  end

  identities do
    identity :unique_daily_report, [:report_date, :platform_id]
  end
end
