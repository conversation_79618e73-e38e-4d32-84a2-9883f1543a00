defmodule Teen.ReportSystem.DailyReport.Changes.GenerateDailyReport do
  @moduledoc """
  生成日报表的变更模块

  根据指定日期和平台，统计并生成日报表数据
  """

  use Ash.Resource.Change
  import Ecto.Query

  @impl true
  def change(changeset, _opts, _context) do
    report_date = Ash.Changeset.get_argument(changeset, :report_date)
    platform_id = Ash.Changeset.get_argument(changeset, :platform_id)

    case generate_report_data(report_date, platform_id) do
      {:ok, report_data} ->
        changeset
        |> Ash.Changeset.change_attributes(report_data)
      {:error, reason} ->
        Ash.Changeset.add_error(changeset, reason)
    end
  end

  defp generate_report_data(report_date, platform_id) do
    try do
      # 获取日期范围
      start_datetime = DateTime.new!(report_date, ~T[00:00:00], "Etc/UTC")
      end_datetime = DateTime.new!(report_date, ~T[23:59:59], "Etc/UTC")

      # 统计各项数据
      report_data = %{
        report_date: report_date,
        platform_id: platform_id,
        new_devices: count_new_devices(start_datetime, end_datetime, platform_id),
        new_registrations: count_new_registrations(start_datetime, end_datetime, platform_id),
        effective_new_users: count_effective_new_users(start_datetime, end_datetime, platform_id),
        mobile_registrations: count_mobile_registrations(start_datetime, end_datetime, platform_id),
        active_users: count_active_users(start_datetime, end_datetime, platform_id),
        effective_active_users: count_effective_active_users(start_datetime, end_datetime, platform_id),
        active_mobile_bindings: count_active_mobile_bindings(start_datetime, end_datetime, platform_id),
        recharge_users: count_recharge_users(start_datetime, end_datetime, platform_id),
        new_recharge_users: count_new_recharge_users(start_datetime, end_datetime, platform_id),
        first_pay_users: count_first_pay_users(start_datetime, end_datetime, platform_id),
        recharge_amount: sum_recharge_amount(start_datetime, end_datetime, platform_id),
        withdrawal_amount: sum_withdrawal_amount(start_datetime, end_datetime, platform_id),
        withdrawal_users: count_withdrawal_users(start_datetime, end_datetime, platform_id)
      }

      # 计算比率和ARPU
      enhanced_data = report_data
      |> calculate_ratios()
      |> calculate_arpu_metrics()
      |> calculate_retention_rates(report_date, platform_id)

      {:ok, enhanced_data}
    rescue
      error ->
        {:error, "生成日报表失败: #{inspect(error)}"}
    end
  end

  # 统计新增设备数
  defp count_new_devices(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的设备记录表来统计
    # 示例实现，需要根据实际数据结构调整
    query = from(u in Cypridina.Accounts.User,
      where: u.inserted_at >= ^start_datetime and u.inserted_at <= ^end_datetime,
      select: count(u.id, :distinct)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计新增注册数
  defp count_new_registrations(start_datetime, end_datetime, platform_id) do
    query = from(u in Cypridina.Accounts.User,
      where: u.inserted_at >= ^start_datetime and u.inserted_at <= ^end_datetime,
      select: count(u.id)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计有效新增用户数（注册后有过游戏行为的用户）
  defp count_effective_new_users(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的游戏记录表来统计
    # 示例：注册后24小时内有游戏记录的用户
    query = from(u in Cypridina.Accounts.User,
      where: u.inserted_at >= ^start_datetime and u.inserted_at <= ^end_datetime,
      select: count(u.id)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    # 这里应该加入游戏行为的关联查询
    Cypridina.Repo.one(query) || 0
  end

  # 统计手机注册绑定数
  defp count_mobile_registrations(start_datetime, end_datetime, platform_id) do
    query = from(u in Cypridina.Accounts.User,
      where: u.inserted_at >= ^start_datetime and u.inserted_at <= ^end_datetime,
      where: not is_nil(u.phone_number),
      select: count(u.id)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计活跃用户数
  defp count_active_users(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的用户活动记录表来统计
    # 示例：当天有登录记录的用户
    query = from(u in Cypridina.Accounts.User,
      where: u.last_login_at >= ^start_datetime and u.last_login_at <= ^end_datetime,
      select: count(u.id, :distinct)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计有效活跃用户数（有游戏行为的活跃用户）
  defp count_effective_active_users(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的游戏记录表来统计
    count_active_users(start_datetime, end_datetime, platform_id)
  end

  # 统计活跃手机绑定数
  defp count_active_mobile_bindings(start_datetime, end_datetime, platform_id) do
    query = from(u in Cypridina.Accounts.User,
      where: u.last_login_at >= ^start_datetime and u.last_login_at <= ^end_datetime,
      where: not is_nil(u.phone_number),
      select: count(u.id, :distinct)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计充值用户数
  defp count_recharge_users(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的充值记录表来统计
    # 示例实现，需要根据实际数据结构调整
    0
  end

  # 统计新增充值用户数
  defp count_new_recharge_users(start_datetime, end_datetime, platform_id) do
    # 首次充值的用户数
    0
  end

  # 统计首付用户数
  defp count_first_pay_users(start_datetime, end_datetime, platform_id) do
    # 首次付费的用户数
    0
  end

  # 统计充值金额
  defp sum_recharge_amount(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的充值记录表来统计
    Decimal.new("0")
  end

  # 统计提现金额
  defp sum_withdrawal_amount(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的提现记录表来统计
    Decimal.new("0")
  end

  # 统计提现用户数
  defp count_withdrawal_users(start_datetime, end_datetime, platform_id) do
    # 这里需要根据实际的提现记录表来统计
    0
  end

  # 计算各种比率
  defp calculate_ratios(report_data) do
    withdrawal_recharge_ratio = if Decimal.gt?(report_data.recharge_amount, 0) do
      Decimal.mult(Decimal.div(report_data.withdrawal_amount, report_data.recharge_amount), 100)
    else
      Decimal.new("0")
    end

    payment_rate = if report_data.active_users > 0 do
      Decimal.mult(Decimal.div(report_data.recharge_users, report_data.active_users), 100)
    else
      Decimal.new("0")
    end

    new_payment_rate = if report_data.new_registrations > 0 do
      Decimal.mult(Decimal.div(report_data.new_recharge_users, report_data.new_registrations), 100)
    else
      Decimal.new("0")
    end

    effective_payment_rate = if report_data.effective_active_users > 0 do
      Decimal.mult(Decimal.div(report_data.recharge_users, report_data.effective_active_users), 100)
    else
      Decimal.new("0")
    end

    effective_new_payment_rate = if report_data.effective_new_users > 0 do
      Decimal.mult(Decimal.div(report_data.new_recharge_users, report_data.effective_new_users), 100)
    else
      Decimal.new("0")
    end

    Map.merge(report_data, %{
      withdrawal_recharge_ratio: withdrawal_recharge_ratio,
      payment_rate: payment_rate,
      new_payment_rate: new_payment_rate,
      effective_payment_rate: effective_payment_rate,
      effective_new_payment_rate: effective_new_payment_rate
    })
  end

  # 计算ARPU相关指标
  defp calculate_arpu_metrics(report_data) do
    arpu = if report_data.active_users > 0 do
      Decimal.div(report_data.recharge_amount, report_data.active_users)
    else
      Decimal.new("0")
    end

    arppu = if report_data.recharge_users > 0 do
      Decimal.div(report_data.recharge_amount, report_data.recharge_users)
    else
      Decimal.new("0")
    end

    effective_arpu = if report_data.effective_active_users > 0 do
      Decimal.div(report_data.recharge_amount, report_data.effective_active_users)
    else
      Decimal.new("0")
    end

    Map.merge(report_data, %{
      arpu: arpu,
      arppu: arppu,
      effective_arpu: effective_arpu
    })
  end

  # 计算留存率
  defp calculate_retention_rates(report_data, report_date, platform_id) do
    # 计算各种留存率
    # 这里需要根据实际的用户活动数据来计算
    retention_day2 = calculate_retention_rate(report_date, 2, platform_id)
    retention_day3 = calculate_retention_rate(report_date, 3, platform_id)
    retention_day7 = calculate_retention_rate(report_date, 7, platform_id)
    retention_day14 = calculate_retention_rate(report_date, 14, platform_id)
    retention_day30 = calculate_retention_rate(report_date, 30, platform_id)

    Map.merge(report_data, %{
      retention_day2: retention_day2,
      retention_day3: retention_day3,
      retention_day7: retention_day7,
      retention_day14: retention_day14,
      retention_day30: retention_day30
    })
  end

  # 计算指定天数的留存率
  defp calculate_retention_rate(base_date, days, platform_id) do
    # 这里需要根据实际的用户活动数据来计算留存率
    # 示例：计算base_date注册的用户在days天后的留存率
    Decimal.new("0")
  end
end
