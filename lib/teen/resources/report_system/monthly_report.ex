defmodule Teen.ReportSystem.MonthlyReport do
  @moduledoc """
  系统月报表资源

  管理系统每月统计数据汇总
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ReportSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :report_month,
      :platform_id,
      :total_new_devices,
      :total_new_registrations,
      :total_active_users,
      :total_recharge_amount,
      :avg_arpu,
      :updated_at
    ]
  end

  postgres do
    table "monthly_reports"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_by_month, args: [:report_month]
    define :get_by_platform, args: [:platform_id]
    define :generate_monthly_report, args: [:report_month, :platform_id]
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [
        :report_month,
        :platform_id,
        :total_new_devices,
        :total_new_registrations,
        :total_effective_new_users,
        :total_mobile_registrations,
        :total_active_users,
        :total_effective_active_users,
        :total_recharge_users,
        :total_recharge_amount,
        :total_withdrawal_amount,
        :avg_payment_rate,
        :avg_arpu,
        :avg_arppu,
        :avg_retention_day2,
        :avg_retention_day7,
        :avg_retention_day30
      ]
    end

    update :update do
      primary? true
      accept [
        :total_new_devices,
        :total_new_registrations,
        :total_effective_new_users,
        :total_mobile_registrations,
        :total_active_users,
        :total_effective_active_users,
        :total_recharge_users,
        :total_recharge_amount,
        :total_withdrawal_amount,
        :avg_payment_rate,
        :avg_arpu,
        :avg_arppu,
        :avg_retention_day2,
        :avg_retention_day7,
        :avg_retention_day30
      ]
    end

    destroy :destroy do
      primary? true
    end

    read :get_by_month do
      argument :report_month, :string, allow_nil?: false
      filter expr(report_month == ^arg(:report_month))
    end

    read :get_by_platform do
      argument :platform_id, :uuid, allow_nil?: false
      filter expr(platform_id == ^arg(:platform_id))
    end

    create :generate_monthly_report do
      argument :report_month, :string, allow_nil?: false
      argument :platform_id, :uuid, allow_nil?: true

      change Teen.ReportSystem.MonthlyReport.Changes.GenerateMonthlyReport
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :report_month, :string do
      allow_nil? false
      public? true
      description "报表月份（YYYY-MM格式）"
    end

    attribute :platform_id, :uuid do
      allow_nil? true
      public? true
      description "平台ID（为空表示全平台统计）"
    end

    # 月度汇总指标
    attribute :total_new_devices, :integer do
      allow_nil? false
      public? true
      description "月度新增设备总数"
      default 0
    end

    attribute :total_new_registrations, :integer do
      allow_nil? false
      public? true
      description "月度新增注册总数"
      default 0
    end

    attribute :total_effective_new_users, :integer do
      allow_nil? false
      public? true
      description "月度有效新增用户总数"
      default 0
    end

    attribute :total_mobile_registrations, :integer do
      allow_nil? false
      public? true
      description "月度手机注册绑定总数"
      default 0
    end

    attribute :total_active_users, :integer do
      allow_nil? false
      public? true
      description "月度活跃用户总数"
      default 0
    end

    attribute :total_effective_active_users, :integer do
      allow_nil? false
      public? true
      description "月度有效活跃用户总数"
      default 0
    end

    attribute :total_recharge_users, :integer do
      allow_nil? false
      public? true
      description "月度充值用户总数"
      default 0
    end

    attribute :total_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "月度充值总额（分）"
      default Decimal.new("0")
    end

    attribute :total_withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "月度提现总额（分）"
      default Decimal.new("0")
    end

    # 月度平均指标
    attribute :avg_payment_rate, :decimal do
      allow_nil? false
      public? true
      description "月度平均付费率（%）"
      default Decimal.new("0")
    end

    attribute :avg_arpu, :decimal do
      allow_nil? false
      public? true
      description "月度平均ARPU"
      default Decimal.new("0")
    end

    attribute :avg_arppu, :decimal do
      allow_nil? false
      public? true
      description "月度平均ARPPU"
      default Decimal.new("0")
    end

    attribute :avg_retention_day2, :decimal do
      allow_nil? false
      public? true
      description "月度平均次日留存率（%）"
      default Decimal.new("0")
    end

    attribute :avg_retention_day7, :decimal do
      allow_nil? false
      public? true
      description "月度平均7日留存率（%）"
      default Decimal.new("0")
    end

    attribute :avg_retention_day30, :decimal do
      allow_nil? false
      public? true
      description "月度平均30日留存率（%）"
      default Decimal.new("0")
    end

    timestamps()
  end

  identities do
    identity :unique_monthly_report, [:report_month, :platform_id]
  end
end
