defmodule Teen.ReportSystem.MonthlyReport.Changes.GenerateMonthlyReport do
  @moduledoc """
  生成月报表的变更模块

  根据指定月份和平台，汇总日报表数据生成月报表
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    report_month = Ash.Changeset.get_argument(changeset, :report_month)
    platform_id = Ash.Changeset.get_argument(changeset, :platform_id)

    case generate_monthly_report_data(report_month, platform_id) do
      {:ok, report_data} ->
        changeset
        |> Ash.Changeset.change_attributes(report_data)
      {:error, reason} ->
        Ash.Changeset.add_error(changeset, reason)
    end
  end

  defp generate_monthly_report_data(report_month, platform_id) do
    try do
      # 解析月份
      [year_str, month_str] = String.split(report_month, "-")
      year = String.to_integer(year_str)
      month = String.to_integer(month_str)

      # 获取月份的开始和结束日期
      start_date = Date.new!(year, month, 1)
      end_date = Date.end_of_month(start_date)

      # 获取该月份的所有日报表数据
      case Teen.ReportSystem.get_date_range_reports(start_date, end_date, platform_id) do
        {:ok, daily_reports} ->
          if length(daily_reports) > 0 do
            monthly_data = aggregate_daily_reports(daily_reports, report_month, platform_id)
            {:ok, monthly_data}
          else
            # 如果没有日报表数据，创建空的月报表
            empty_data = create_empty_monthly_report(report_month, platform_id)
            {:ok, empty_data}
          end
        {:error, reason} ->
          {:error, "获取日报表数据失败: #{inspect(reason)}"}
      end
    rescue
      error ->
        {:error, "生成月报表失败: #{inspect(error)}"}
    end
  end

  defp aggregate_daily_reports(daily_reports, report_month, platform_id) do
    %{
      report_month: report_month,
      platform_id: platform_id,
      
      # 汇总数据
      total_new_devices: sum_field(daily_reports, :new_devices),
      total_new_registrations: sum_field(daily_reports, :new_registrations),
      total_effective_new_users: sum_field(daily_reports, :effective_new_users),
      total_mobile_registrations: sum_field(daily_reports, :mobile_registrations),
      total_active_users: sum_field(daily_reports, :active_users),
      total_effective_active_users: sum_field(daily_reports, :effective_active_users),
      total_recharge_users: sum_field(daily_reports, :recharge_users),
      total_recharge_amount: sum_decimal_field(daily_reports, :recharge_amount),
      total_withdrawal_amount: sum_decimal_field(daily_reports, :withdrawal_amount),
      
      # 平均数据
      avg_payment_rate: avg_decimal_field(daily_reports, :payment_rate),
      avg_arpu: avg_decimal_field(daily_reports, :arpu),
      avg_arppu: avg_decimal_field(daily_reports, :arppu),
      avg_retention_day2: avg_decimal_field(daily_reports, :retention_day2),
      avg_retention_day7: avg_decimal_field(daily_reports, :retention_day7),
      avg_retention_day30: avg_decimal_field(daily_reports, :retention_day30)
    }
  end

  defp create_empty_monthly_report(report_month, platform_id) do
    %{
      report_month: report_month,
      platform_id: platform_id,
      total_new_devices: 0,
      total_new_registrations: 0,
      total_effective_new_users: 0,
      total_mobile_registrations: 0,
      total_active_users: 0,
      total_effective_active_users: 0,
      total_recharge_users: 0,
      total_recharge_amount: Decimal.new("0"),
      total_withdrawal_amount: Decimal.new("0"),
      avg_payment_rate: Decimal.new("0"),
      avg_arpu: Decimal.new("0"),
      avg_arppu: Decimal.new("0"),
      avg_retention_day2: Decimal.new("0"),
      avg_retention_day7: Decimal.new("0"),
      avg_retention_day30: Decimal.new("0")
    }
  end

  defp sum_field(reports, field) do
    Enum.reduce(reports, 0, fn report, acc ->
      acc + Map.get(report, field, 0)
    end)
  end

  defp sum_decimal_field(reports, field) do
    Enum.reduce(reports, Decimal.new("0"), fn report, acc ->
      value = Map.get(report, field, Decimal.new("0"))
      Decimal.add(acc, value)
    end)
  end

  defp avg_decimal_field(reports, field) do
    if length(reports) > 0 do
      sum = sum_decimal_field(reports, field)
      Decimal.div(sum, length(reports))
    else
      Decimal.new("0")
    end
  end
end
