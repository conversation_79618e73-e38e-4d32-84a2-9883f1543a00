defmodule Teen.ReportSystem.RealtimeStats do
  @moduledoc """
  实时数据统计资源

  管理系统实时统计数据，包括：
  - 在线用户统计
  - 实时充值数据
  - 实时游戏数据
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ReportSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :stat_time,
      :platform_id,
      :online_users,
      :active_games,
      :realtime_recharge,
      :updated_at
    ]
  end

  postgres do
    table "realtime_stats"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_latest_stats
    define :get_by_platform, args: [:platform_id]
    define :update_realtime_stats, args: [:platform_id]
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [
        :stat_time,
        :platform_id,
        :online_users,
        :active_games,
        :active_rooms,
        :realtime_recharge,
        :realtime_withdrawal,
        :today_new_users,
        :today_active_users,
        :today_recharge_users,
        :today_recharge_amount
      ]
    end

    update :update do
      primary? true
      accept [
        :online_users,
        :active_games,
        :active_rooms,
        :realtime_recharge,
        :realtime_withdrawal,
        :today_new_users,
        :today_active_users,
        :today_recharge_users,
        :today_recharge_amount
      ]
    end

    destroy :destroy do
      primary? true
    end

    read :get_latest_stats do
      filter expr(stat_time >= ago(1, :hour))
      prepare build(sort: [stat_time: :desc])
    end

    read :get_by_platform do
      argument :platform_id, :uuid, allow_nil?: false
      filter expr(platform_id == ^arg(:platform_id))
    end

    create :update_realtime_stats do
      argument :platform_id, :uuid, allow_nil?: true
      change Teen.ReportSystem.RealtimeStats.Changes.UpdateRealtimeStats
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :stat_time, :utc_datetime_usec do
      allow_nil? false
      public? true
      description "统计时间"
      default &DateTime.utc_now/0
    end

    attribute :platform_id, :uuid do
      allow_nil? true
      public? true
      description "平台ID（为空表示全平台统计）"
    end

    # 在线统计
    attribute :online_users, :integer do
      allow_nil? false
      public? true
      description "在线用户数"
      default 0
    end

    attribute :active_games, :integer do
      allow_nil? false
      public? true
      description "活跃游戏数"
      default 0
    end

    attribute :active_rooms, :integer do
      allow_nil? false
      public? true
      description "活跃房间数"
      default 0
    end

    # 实时充值提现
    attribute :realtime_recharge, :decimal do
      allow_nil? false
      public? true
      description "实时充值金额（分）"
      default Decimal.new("0")
    end

    attribute :realtime_withdrawal, :decimal do
      allow_nil? false
      public? true
      description "实时提现金额（分）"
      default Decimal.new("0")
    end

    # 今日统计
    attribute :today_new_users, :integer do
      allow_nil? false
      public? true
      description "今日新增用户"
      default 0
    end

    attribute :today_active_users, :integer do
      allow_nil? false
      public? true
      description "今日活跃用户"
      default 0
    end

    attribute :today_recharge_users, :integer do
      allow_nil? false
      public? true
      description "今日充值用户"
      default 0
    end

    attribute :today_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "今日充值金额（分）"
      default Decimal.new("0")
    end

    timestamps()
  end

  calculations do
    calculate :online_users_display, :string do
      public? true
      description "在线用户显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "#{record.online_users}人在线"
        end)
      end
    end

    calculate :recharge_amount_display, :string do
      public? true
      description "充值金额显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          amount = Decimal.to_float(record.today_recharge_amount) / 100
          "¥#{:erlang.float_to_binary(amount, decimals: 2)}"
        end)
      end
    end

    calculate :stat_time_display, :string do
      public? true
      description "统计时间显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.stat_time
          |> DateTime.shift_zone!("Asia/Shanghai")
          |> DateTime.to_string()
        end)
      end
    end
  end
end
