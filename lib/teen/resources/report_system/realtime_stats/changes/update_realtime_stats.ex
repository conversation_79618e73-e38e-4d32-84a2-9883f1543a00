defmodule Teen.ReportSystem.RealtimeStats.Changes.UpdateRealtimeStats do
  @moduledoc """
  更新实时统计数据的变更模块

  收集当前系统的实时数据并更新统计
  """

  use Ash.Resource.Change
  import Ecto.Query

  @impl true
  def change(changeset, _opts, _context) do
    platform_id = Ash.Changeset.get_argument(changeset, :platform_id)

    case collect_realtime_data(platform_id) do
      {:ok, stats_data} ->
        changeset
        |> Ash.Changeset.change_attributes(stats_data)
      {:error, reason} ->
        Ash.Changeset.add_error(changeset, reason)
    end
  end

  defp collect_realtime_data(platform_id) do
    try do
      current_time = DateTime.utc_now()
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00], "Etc/UTC")
      
      stats_data = %{
        stat_time: current_time,
        platform_id: platform_id,
        
        # 在线统计
        online_users: count_online_users(platform_id),
        active_games: count_active_games(platform_id),
        active_rooms: count_active_rooms(platform_id),
        
        # 实时充值提现（这里可以根据实际需求调整时间范围）
        realtime_recharge: sum_recent_recharge(platform_id),
        realtime_withdrawal: sum_recent_withdrawal(platform_id),
        
        # 今日统计
        today_new_users: count_today_new_users(today_start, platform_id),
        today_active_users: count_today_active_users(today_start, platform_id),
        today_recharge_users: count_today_recharge_users(today_start, platform_id),
        today_recharge_amount: sum_today_recharge_amount(today_start, platform_id)
      }
      
      {:ok, stats_data}
    rescue
      error ->
        {:error, "收集实时数据失败: #{inspect(error)}"}
    end
  end

  # 统计在线用户数
  defp count_online_users(platform_id) do
    # 这里需要根据实际的在线用户追踪机制来实现
    # 示例：统计最近5分钟内有活动的用户
    five_minutes_ago = DateTime.add(DateTime.utc_now(), -5, :minute)
    
    query = from(u in Cypridina.Accounts.User,
      where: u.last_login_at >= ^five_minutes_ago,
      select: count(u.id, :distinct)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计活跃游戏数
  defp count_active_games(platform_id) do
    # 这里需要根据实际的游戏记录表来统计
    # 示例：统计最近10分钟内有游戏记录的游戏数
    0
  end

  # 统计活跃房间数
  defp count_active_rooms(platform_id) do
    # 这里需要根据实际的房间系统来统计
    # 示例：统计当前有用户的房间数
    0
  end

  # 统计最近充值金额（比如最近1小时）
  defp sum_recent_recharge(platform_id) do
    # 这里需要根据实际的充值记录表来统计
    # 示例：统计最近1小时的充值金额
    Decimal.new("0")
  end

  # 统计最近提现金额（比如最近1小时）
  defp sum_recent_withdrawal(platform_id) do
    # 这里需要根据实际的提现记录表来统计
    # 示例：统计最近1小时的提现金额
    Decimal.new("0")
  end

  # 统计今日新增用户数
  defp count_today_new_users(today_start, platform_id) do
    query = from(u in Cypridina.Accounts.User,
      where: u.inserted_at >= ^today_start,
      select: count(u.id)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计今日活跃用户数
  defp count_today_active_users(today_start, platform_id) do
    query = from(u in Cypridina.Accounts.User,
      where: u.last_login_at >= ^today_start,
      select: count(u.id, :distinct)
    )

    query = if platform_id do
      from(u in query, where: u.platform_id == ^platform_id)
    else
      query
    end

    Cypridina.Repo.one(query) || 0
  end

  # 统计今日充值用户数
  defp count_today_recharge_users(today_start, platform_id) do
    # 这里需要根据实际的充值记录表来统计
    # 示例实现，需要根据实际数据结构调整
    0
  end

  # 统计今日充值金额
  defp sum_today_recharge_amount(today_start, platform_id) do
    # 这里需要根据实际的充值记录表来统计
    # 示例实现，需要根据实际数据结构调整
    Decimal.new("0")
  end
end
