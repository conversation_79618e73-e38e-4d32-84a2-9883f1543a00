defmodule Cypridina.Repo.Migrations.InitDb do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:game_control_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :game_id, :bigint, null: false
      add :game_type, :bigint, null: false
      add :game_name, :text, null: false
      add :base_inventory, :decimal, null: false, default: "1000000"
      add :control_weight, :bigint, null: false, default: 500
      add :dark_tax_rate, :bigint, null: false, default: -50
      add :winner_tax_rate, :decimal, null: false, default: "0.05"
      add :collect_line_max, :bigint, null: false, default: 30000
      add :collect_line_min, :bigint, null: false, default: 5000
      add :collect_line_ratio, :decimal, null: false, default: "0.2"
      add :pre_collect_line_ratio, :decimal, null: false, default: "0.7"
      add :release_line_max, :bigint, null: false, default: 30000
      add :release_line_min, :bigint, null: false, default: 5000
      add :release_line_ratio, :decimal, null: false, default: "0.2"
      add :pre_release_line_ratio, :decimal, null: false, default: "0.7"
      add :long_weight, :bigint, null: false, default: 33
      add :hu_weight, :bigint, null: false, default: 33
      add :he_weight, :bigint, null: false, default: 34
      add :is_active, :boolean, null: false, default: true

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:game_control_configs, [:game_id, :game_type],
             name: "game_control_configs_unique_game_and_type_index"
           )

    create table(:share_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :config_key, :text, null: false
      add :config_name, :text, null: false
      add :config_type, :text, null: false
      add :config_value, :map, null: false, default: %{}
      add :description, :text
      add :status, :bigint, null: false, default: 1
      add :sort_order, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:share_configs, [:config_key],
             name: "share_configs_unique_config_key_index"
           )

    create table(:user_turnovers, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :required_turnover, :decimal, null: false, default: "0"
      add :completed_turnover, :decimal, null: false, default: "0"
      add :status, :bigint, null: false, default: 1
      add :last_bonus_type, :text
      add :last_bonus_amount, :decimal
      add :last_bet_amount, :decimal
      add :last_bet_time, :utc_datetime
      add :reset_time, :utc_datetime
      add :reset_reason, :text
      add :reset_count, :bigint, null: false, default: 0
      add :total_bonus_received, :decimal, null: false, default: "0"
      add :total_bets_made, :decimal, null: false, default: "0"
      add :bonusmoney, :bigint, null: false, default: 0
      add :bonuscashmoney, :bigint, null: false, default: 0
      add :winningmoney, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:scratch_card_task_levels, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :task_level, :bigint, null: false
      add :recharge_amount, :decimal, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :activity_id, :uuid
    end

    create table(:cdkey_batches, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :batch_name, :text, null: false
      add :template_name, :text, null: false
      add :description, :text
      add :total_count, :bigint, null: false, default: 0
      add :used_count, :bigint, null: false, default: 0
      add :expired_count, :bigint, null: false, default: 0
      add :valid_from, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
      add :valid_to, :utc_datetime, null: false
      add :creator_id, :uuid
      add :creator_name, :text
      add :status, :text, null: false, default: "active"
      add :generation_config, :map, default: %{}
      add :last_updated_at, :utc_datetime
      add :metadata, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :template_id, :uuid
    end

    create table(:sign_in_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :bigint, null: false, default: 1
      add :daily_rewards, {:array, :map}, default: []
      add :consecutive_rewards, {:array, :map}, default: []
      add :monthly_rewards, {:array, :map}, default: []
      add :vip_bonus_rate, :decimal, null: false, default: "0"
      add :max_consecutive_days, :bigint, null: false, default: 30

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:sign_in_activities, [:activity_name],
             name: "sign_in_activities_unique_activity_name_index"
           )

    create table(:bank_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :bank_code, :text, null: false
      add :icon_url, :text
      add :status, :bigint, null: false, default: 1
      add :sort_order, :bigint, null: false, default: 0
      add :min_amount, :decimal, null: false, default: "100"
      add :max_amount, :decimal, null: false, default: "100000"
      add :fee_rate, :decimal, null: false, default: "0"
      add :config_data, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:bank_configs, [:bank_code], name: "bank_configs_unique_bank_code_index")

    create table(:withdrawal_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :config_name, :text, null: false
      add :payment_method, :text, null: false
      add :min_amount, :decimal, null: false
      add :max_amount, :decimal, null: false
      add :daily_limit, :decimal
      add :monthly_limit, :decimal
      add :fee_rate, :decimal, null: false, default: "0"
      add :tax_rate, :decimal, null: false, default: "0"
      add :min_fee_amount, :decimal
      add :max_fee_amount, :decimal
      add :status, :bigint, null: false, default: 1
      add :vip_level_required, :bigint, default: 0
      add :turnover_multiplier, :decimal, null: false, default: "1"
      add :min_deposit_required, :decimal
      add :processing_time_hours, :bigint, null: false, default: 24
      add :auto_approve_limit, :decimal
      add :gateway_channel_id, :text
      add :description, :text
      add :business_hours_only, :boolean, null: false, default: false
      add :weekend_processing, :boolean, null: false, default: true
      add :vip_fee_discount, :decimal
      add :vip_processing_priority, :boolean, null: false, default: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:withdrawal_configs, [:config_name],
             name: "withdrawal_configs_unique_config_name_index"
           )

    create unique_index(:withdrawal_configs, [:payment_method, :config_name],
             name: "withdrawal_configs_unique_payment_method_config_index"
           )

    create table(:reward_claim_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :activity_type, :text, null: false
      add :activity_id, :uuid
      add :reward_type, :text, null: false
      add :reward_amount, :decimal, null: false
      add :reward_data, :map
      add :claimed_at, :utc_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:agent_relationships, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :agent_id, :uuid, null: false
      add :subordinate_id, :uuid, null: false
      add :level, :bigint, null: false, default: 1
      add :commission_rate, :decimal, null: false, default: "0.05"
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:weekly_card_purchases, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :card_id, :uuid, null: false
      add :purchase_amount, :decimal, null: false
      add :payment_order_id, :text
      add :transaction_id, :text
      add :purchase_status, :text, null: false, default: "pending"
      add :start_date, :date, null: false
      add :end_date, :date, null: false
      add :daily_claims_count, :bigint, null: false, default: 0
      add :last_claim_date, :date
      add :purchased_at, :utc_datetime, null: false
      add :activated_at, :utc_datetime
      add :last_claimed_at, :utc_datetime
      add :expired_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_activity_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :activity_type, :text, null: false
      add :activity_id, :uuid, null: false
      add :participation_data, :map, default: %{}
      add :reward_claimed, :boolean, null: false, default: false
      add :claimed_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:tokens, primary_key: false) do
      add :jti, :text, null: false, primary_key: true
      add :subject, :text, null: false
      add :expires_at, :utc_datetime, null: false
      add :purpose, :text, null: false
      add :extra_data, :map

      add :created_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:game_tasks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :task_name, :text, null: false
      add :game_id, :bigint, default: 1
      add :task_type, :text, null: false
      add :required_count, :bigint, null: false, default: 1
      add :max_claims, :bigint, null: false, default: 1
      add :reward_amount, :decimal, null: false, default: "0"
      add :status, :text, null: false, default: "enabled"
      add :target_value, :bigint, default: 1
      add :reward_type, :text, default: "coins"
      add :game_config_id, :uuid
      add :is_active, :boolean, default: true
      add :start_date, :date, default: fragment("CURRENT_DATE")
      add :end_date, :date

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:game_tasks, [:game_id, :task_type],
             name: "game_tasks_unique_game_task_index"
           )

    create table(:vip_levels, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :level, :bigint, null: false
      add :level_name, :text, null: false
      add :recharge_requirement, :decimal, null: false, default: "0"
      add :daily_bonus, :decimal, null: false, default: "0"
      add :exchange_rate_bonus, :decimal, null: false, default: "0"
      add :recharge_bonus, :decimal, null: false, default: "0"
      add :icon_url, :text
      add :description, :text
      add :privileges, {:array, :text}, default: []
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:vip_levels, [:level], name: "vip_levels_unique_level_index")

    create table(:channels, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :channel_id, :text, null: false
      add :channel_name, :text, null: false
      add :package_name, :text
      add :description, :text
      add :config, :map, default: %{}
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:channels, [:channel_id], name: "channels_unique_channel_id_index")

    create unique_index(:channels, [:package_name],
             name: "channels_unique_package_name_index",
             where: "(package_name IS NOT NULL)"
           )

    create table(:vip_gifts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :vip_level, :bigint, null: false
      add :daily_reward, :decimal, null: false, default: "0"
      add :weekly_reward, :decimal, null: false, default: "0"
      add :monthly_reward, :decimal, null: false, default: "0"
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:vip_gifts, [:vip_level], name: "vip_gifts_unique_vip_level_index")

    create table(:scratch_card_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:scratch_card_task_levels) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_levels_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:scratch_card_task_levels, [:activity_id, :task_level],
             name: "scratch_card_task_levels_unique_activity_level_index"
           )

    alter table(:scratch_card_activities) do
      add :activity_title, :text, null: false
      add :claimable_count, :bigint, null: false, default: 30
      add :reward_probability, :decimal, null: false, default: "10"
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:ip_whitelists, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :ip_address, :text
      add :ip_range, :text
      add :type, :text, null: false, default: "single"
      add :status, :bigint, null: false, default: 1
      add :description, :text
      add :created_by, :uuid
      add :last_used_at, :utc_datetime
      add :use_count, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:ip_whitelists, [:ip_address, :type],
             name: "ip_whitelists_unique_ip_address_index",
             where: "(type = 'single')"
           )

    create unique_index(:ip_whitelists, [:ip_range, :type],
             name: "ip_whitelists_unique_ip_range_index",
             where: "(type = 'range')"
           )

    create table(:user_bans, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :ban_type, :bigint, null: false
      add :reason, :text, null: false
      add :status, :bigint, null: false, default: 1
      add :cash_amount, :decimal, null: false, default: "0"
      add :bank_amount, :decimal, null: false, default: "0"
      add :ip_address, :text
      add :operator_id, :uuid, null: false
      add :banned_at, :utc_datetime, null: false
      add :expires_at, :utc_datetime
      add :unbanned_at, :utc_datetime
      add :unban_operator_id, :uuid
      add :unban_reason, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:product_templates, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :template_name, :text, null: false
      add :product_type, :text, null: false
      add :default_config, :map, null: false, default: %{}
      add :config_schema, :map, default: %{}
      add :is_default, :boolean, null: false, default: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:product_templates, [:product_type, :is_default],
             name: "product_templates_unique_default_per_type_index",
             where: "(is_default = true)"
           )

    create table(:payment_gateway, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :gateway_name, :text, null: false
      add :gateway_code, :text, null: false
      add :gateway_type, :text, null: false, default: "recharge"
      add :priority, :bigint, null: false, default: 100
      add :timeout_seconds, :bigint, null: false, default: 30
      add :merchant_id, :text, null: false
      add :merchant_key, :text, null: false
      add :gateway_url, :text, null: false
      add :create_order_path, :text, null: false
      add :query_order_path, :text, null: false
      add :callback_ip, :text, null: false
      add :recharge_channel, :text, null: false
      add :withdrawal_channel, :text, null: false
      add :supported_currencies, {:array, :text}, null: false, default: []
      add :supported_payment_methods, {:array, :text}, null: false, default: []
      add :min_amount, :decimal, null: false, default: "0"
      add :max_amount, :decimal, null: false, default: "0"
      add :fee_rate, :decimal, null: false, default: "0"
      add :status, :text, null: false, default: "active"
      add :config_data, :map, null: false, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:payment_gateway, [:gateway_code],
             name: "payment_gateway_unique_gateway_code_index"
           )

    create table(:broke_award_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :min_balance_threshold, :decimal, null: false, default: "1000"
      add :award_amount, :decimal, null: false, default: "500"
      add :max_claims_per_day, :bigint, null: false, default: 3
      add :max_claims_total, :bigint, null: false, default: 10
      add :cooldown_hours, :bigint, null: false, default: 8
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_rewards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :reward_type, :text, null: false
      add :source_type, :text, null: false
      add :source_id, :uuid
      add :reward_amount, :decimal, null: false
      add :reward_data, :map
      add :description, :text
      add :is_pending, :boolean, null: false, default: false
      add :status, :text, null: false, default: "pending"
      add :expires_at, :utc_datetime
      add :claimed_at, :utc_datetime
      add :distributed_at, :utc_datetime
      add :created_at, :utc_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:races, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :issue, :text, null: false
      add :issue_id, :text
      add :positions, {:array, :text}, default: []
      add :speeds, {:array, :text}, default: []
      add :end_times, {:array, :text}, default: []
      add :status, :bigint, default: 0
      add :start_time, :utc_datetime_usec
      add :end_time, :utc_datetime_usec
      add :issue_end_time, :utc_datetime_usec
      add :order_end_time, :utc_datetime_usec
      add :bet_amount_map, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:users_versions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :version_action_type, :text, null: false
      add :version_action_name, :text, null: false
      add :channel_id, :text
      add :version_source_id, :uuid, null: false
      add :changes, :map

      add :version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:platforms, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :platform_number, :text, null: false
      add :platform_name, :text, null: false
      add :platform_announcement, :text
      add :agent_recharge_switch, :bigint, null: false, default: 0
      add :agent_platform_number, :text
      add :login_platform_number, :text
      add :show_qr_code, :bigint, null: false, default: 1
      add :default_download_url, :text
      add :ios_download_url, :text
      add :android_download_url, :text
      add :status, :bigint, null: false, default: 1
      add :sort_order, :bigint, null: false, default: 0
      add :config_data, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:platforms, [:platform_name],
             name: "platforms_unique_platform_name_index"
           )

    create unique_index(:platforms, [:platform_number],
             name: "platforms_unique_platform_number_index"
           )

    create table(:recharge_tasks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :recharge_amount, :decimal, null: false
      add :status, :text, null: false, default: "enabled"

      add :recharge_type, :text, null: false, default: "single_recharge"
      add :rewards, {:array, :map}, null: false, default: []
      add :start_date, :date, null: false, default: fragment("CURRENT_DATE")
      add :end_date, :date, null: false, default: fragment("CURRENT_DATE + INTERVAL '30 days'")
      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:recharge_tasks, [:recharge_amount],
             name: "recharge_tasks_unique_recharge_amount_index"
           )

    create table(:products, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :description, :text
      add :product_type, :text, null: false
      add :category, :text
      add :sku, :text, null: false
      add :price, :decimal, null: false
      add :currency, :text, null: false, default: "inr"
      add :product_config, :map, null: false, default: %{}
      add :display_config, :map, default: %{}
      add :sort_order, :bigint, null: false, default: 0
      add :status, :text, null: false, default: "active"
      add :created_at, :utc_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:products, [:sku], name: "products_unique_sku_index")

    create table(:user_bank_cards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :bank_id, :uuid, null: false
      add :account_name, :text, null: false
      add :account_number, :text, null: false
      add :is_default, :boolean, null: false, default: false
      add :status, :bigint, null: false, default: 1
      add :verified_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:cdkey_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :code, :text, null: false
      add :batch_name, :text, null: false
      add :reward_type, :text, null: false
      add :reward_amount, :decimal, null: false, default: "0"
      add :reward_items, :map, default: %{}
      add :max_uses, :bigint, null: false, default: 1
      add :valid_from, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
      add :valid_to, :utc_datetime, null: false
      add :metadata, :map, default: %{}
      add :status, :text, null: false, default: "active"
      add :used_count, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :used_by_user_id, :uuid
    end

    create table(:first_recharge_gifts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :title, :text, null: false
      add :limit_days, :bigint, null: false, default: 7
      add :reward_coins, :decimal, null: false
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:verification_codes, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :phone_number, :text, null: false
      add :code, :text, null: false
      add :code_type, :bigint, null: false
      add :status, :bigint, null: false, default: 0
      add :expires_at, :utc_datetime, null: false
      add :sent_at, :utc_datetime
      add :used_at, :utc_datetime
      add :failure_reason, :text
      add :ip_address, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:verification_codes, [:phone_number, :code, :inserted_at],
             name: "verification_codes_unique_phone_code_time_index"
           )

    create table(:ledger_transfers, primary_key: false) do
      add :id, :binary, null: false, primary_key: true
      add :amount, :money_with_currency, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :transaction_type, :text, null: false
      add :description, :text
      add :metadata, :map
      add :status, :text, null: false, default: "completed"
      add :business_id, :text

      add :timestamp, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :from_account_id, :uuid
      add :to_account_id, :uuid
    end

    create table(:wallet_control_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :game_id, :bigint, null: false
      add :collect_ratio, :bigint, null: false, default: 20
      add :release_ratio, :bigint, null: false, default: 25
      add :sensitivity, :bigint, null: false, default: 10
      add :is_enabled, :boolean, null: false, default: true
      add :description, :text
      add :created_by, :uuid
      add :updated_by, :uuid

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_login_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :login_date, :date, null: false
      add :login_time, :utc_datetime, null: false
      add :consecutive_days, :bigint, null: false, default: 1
      add :is_first_today, :boolean, null: false, default: true
      add :ip_address, :text
      add :device_type, :text
      add :client_info, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:cdkey_templates, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :description, :text
      add :template_type, :text, null: false, default: "general"
      add :code_format, :map, null: false
      add :reward_config, :map, null: false
      add :usage_conditions, :map, null: false
      add :generation_config, :map, null: false
      add :max_generate_count, :bigint
      add :generated_count, :bigint, null: false, default: 0
      add :last_generated_at, :utc_datetime
      add :status, :text, null: false, default: "active"
      add :metadata, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:cdkey_templates, [:name], name: "cdkey_templates_unique_name_index")

    create table(:scratch_card_task_rounds, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :round_number, :bigint, null: false
      add :recharge_amount, :decimal, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :activity_id,
          references(:scratch_card_activities,
            column: :id,
            name: "scratch_card_task_rounds_activity_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create unique_index(:scratch_card_task_rounds, [:activity_id, :round_number],
             name: "scratch_card_task_rounds_unique_activity_round_index"
           )

    create table(:user_identities, primary_key: false) do
      add :refresh_token, :text
      add :access_token_expires_at, :utc_datetime_usec
      add :access_token, :text
      add :uid, :text, null: false
      add :strategy, :text, null: false
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid
    end

    create table(:cdkey_claim_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :username, :text, null: false
      add :claimed_coins, :decimal, null: false
      add :claimed_at, :utc_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :cdkey_id, :uuid
    end

    create table(:user_vip_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :current_vip_level, :bigint, null: false, default: 0
      add :vip_experience, :decimal, null: false, default: "0"
      add :total_recharge_amount, :decimal, null: false, default: "0"
      add :total_rewards_received, :decimal, null: false, default: "0"
      add :level_up_count, :bigint, null: false, default: 0
      add :last_level_up_at, :utc_datetime
      add :last_recharge_at, :utc_datetime
      add :last_reward_at, :utc_datetime
      add :daily_login_streak, :bigint, null: false, default: 0
      add :last_daily_bonus_at, :date
      add :monthly_recharge_amount, :decimal, null: false, default: "0"
      add :monthly_rewards_received, :decimal, null: false, default: "0"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:share_settlements, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :share_id, :uuid, null: false
      add :settlement_type, :text, null: false
      add :reward_amount, :decimal, null: false, default: "0"
      add :bonus_amount, :decimal, null: false, default: "0"
      add :total_amount, :decimal, null: false, default: "0"
      add :settlement_period, :text, null: false
      add :status, :bigint, null: false, default: 0
      add :settlement_date, :date
      add :payment_date, :date
      add :remarks, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:robot_entities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :robot_id, :bigint, null: false
      add :nickname, :text, null: false
      add :avatar_id, :bigint, default: 1
      add :level, :bigint, default: 1
      add :status, :bigint, default: 0
      add :current_game_type, :text
      add :current_room_id, :text
      add :seat_number, :bigint
      add :current_points, :bigint, default: 100_000
      add :min_points_threshold, :bigint, default: 5000
      add :last_bet_amount, :bigint, default: 0
      add :status_changed_at, :utc_datetime_usec
      add :game_joined_at, :utc_datetime_usec
      add :last_activity_at, :utc_datetime_usec
      add :recycle_reason, :text
      add :recycled_by, :text
      add :robot_config, :map, default: %{}
      add :is_auto_created, :boolean, default: true
      add :creator_admin_id, :text
      add :tags, {:array, :text}, default: []
      add :is_enabled, :boolean, default: true

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create index(:robot_entities, [:status, :is_enabled])

    create index(:robot_entities, [:current_room_id])

    create index(:robot_entities, [:current_game_type])

    create index(:robot_entities, [:is_enabled])

    create index(:robot_entities, [:status])

    create unique_index(:robot_entities, [:robot_id],
             name: "robot_entities_unique_robot_id_index"
           )

    create table(:permissions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :code, :text, null: false
      add :type, :text, null: false

      add :parent_id,
          references(:permissions,
            column: :id,
            name: "permissions_parent_id_fkey",
            type: :uuid,
            prefix: "public"
          )

      add :path, :text
      add :component, :text
      add :icon, :text
      add :sort_order, :bigint, null: false, default: 0
      add :status, :bigint, null: false, default: 1
      add :description, :text
      add :is_external, :boolean, null: false, default: false
      add :is_hidden, :boolean, null: false, default: false
      add :meta_data, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:permissions, [:code], name: "permissions_unique_code_index")

    create table(:roles, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :code, :text, null: false
      add :level, :bigint, null: false, default: 0
      add :status, :bigint, null: false, default: 1
      add :description, :text
      add :permission_codes, {:array, :text}, default: []
      add :menu_permissions, {:array, :text}, default: []
      add :data_permissions, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:roles, [:code], name: "roles_unique_code_index")

    create unique_index(:roles, [:name], name: "roles_unique_name_index")

    create table(:user_vip_infos, primary_key: false) do
      add :id, :bigserial, null: false, primary_key: true
      add :vip_level, :bigint, null: false, default: 0
      add :experience, :decimal, null: false, default: "0"
      add :total_experience, :decimal, null: false, default: "0"
      add :level_up_time, :utc_datetime
      add :daily_bonus_claimed, :boolean, null: false, default: false
      add :last_bonus_claim_time, :utc_datetime
      add :monthly_recharge, :decimal, null: false, default: "0"
      add :monthly_withdrawal, :decimal, null: false, default: "0"
      add :today_withdrawal_times, :bigint, null: false, default: 0
      add :today_withdrawal_amount, :decimal, null: false, default: "0"
      add :statistics, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id, :uuid
    end

    create table(:alert_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :alert_type, :bigint, null: false
      add :alert_type_name, :text, null: false
      add :alert_content, :text, null: false
      add :alert_data, :map
      add :alert_time, :utc_datetime, null: false
      add :status, :bigint, null: false, default: 0
      add :read_at, :utc_datetime
      add :severity, :bigint, null: false, default: 2
      add :source, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:alert_records, [:alert_time, :alert_type],
             name: "alert_records_unique_alert_time_type_index"
           )

    create table(:payment_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :gateway_id,
          references(:payment_gateway,
            column: :id,
            name: "payment_configs_gateway_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :gateway_name, :text, null: false
      add :payment_type, :text, null: false
      add :payment_type_name, :text, null: false
      add :min_amount, :decimal, null: false
      add :max_amount, :decimal, null: false
      add :recharge_range, :text
      add :bonus_range, :text
      add :fee_rate, :decimal, null: false, default: "0"
      add :deduction_rate, :decimal, null: false, default: "0"
      add :status, :bigint, null: false, default: 1
      add :sort_order, :bigint, null: false, default: 0
      add :config_data, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:payment_configs, [:gateway_id, :payment_type],
             name: "payment_configs_unique_gateway_payment_type_index"
           )

    create table(:wheel_prize_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :prize_type, :text, null: false
      add :prize_value, :decimal, null: false
      add :probability, :decimal, null: false
      add :sort_order, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_purchases, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :product_id, :uuid, null: false
      add :product_name, :text, null: false
      add :product_type, :text, null: false
      add :product_config, :map, null: false, default: %{}
      add :original_price, :decimal, null: false
      add :paid_amount, :decimal, null: false
      add :currency, :text, null: false, default: "inr"
      add :payment_order_id, :text
      add :transaction_id, :text
      add :payment_status, :text, null: false, default: "pending"
      add :delivery_status, :text, null: false, default: "pending"
      add :delivery_data, :map, default: %{}
      add :refund_reason, :text
      add :purchased_at, :utc_datetime, null: false
      add :paid_at, :utc_datetime
      add :delivered_at, :utc_datetime
      add :refunded_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:operation_logs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :admin_user_id, :uuid, null: false
      add :operation_type, :text, null: false
      add :module, :text, null: false
      add :description, :text, null: false
      add :ip_address, :text
      add :user_agent, :text
      add :request_data, :map, default: %{}
      add :response_data, :map, default: %{}
      add :execution_time, :bigint
      add :login_result, :text
      add :error_message, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:role_permissions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :role_id,
          references(:roles,
            column: :id,
            name: "role_permissions_role_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :permission_id,
          references(:permissions,
            column: :id,
            name: "role_permissions_permission_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :granted_by, :uuid
      add :granted_at, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:role_permissions, [:role_id, :permission_id],
             name: "role_permissions_unique_role_permission_index"
           )

    create table(:racing_game_stocks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :racer_id, :text, null: false
      add :quantity, :bigint, null: false, default: 0
      add :total_cost, :decimal, null: false, default: "0"
      add :transaction_target_id, :text
      add :transaction_target_type, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:ledger_balances, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :balance, :money_with_currency

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :transfer_id,
          references(:ledger_transfers,
            column: :id,
            name: "ledger_balances_transfer_id_fkey",
            type: :binary,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false

      add :account_id, :uuid, null: false
    end

    create table(:free_cash_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :base_amount, :decimal, null: false, default: "1000"
      add :invitation_bonus, :decimal, null: false, default: "100"
      add :min_invites, :bigint, null: false, default: 0
      add :max_invites, :bigint, null: false, default: 50
      add :max_claims_per_user, :bigint, null: false, default: 1
      add :invite_requirements, :map, null: false
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:loss_rebate_jars, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :title, :text, null: false
      add :max_claims, :bigint, null: false, default: 1
      add :loss_threshold, :decimal, null: false
      add :rebate_percentage, :decimal, null: false, default: "10"
      add :max_rebate, :decimal, null: false
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_tags, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :description, :text
      add :color, :text, null: false, default: "#007bff"
      add :status, :bigint, null: false, default: 1
      add :sort_order, :bigint, null: false, default: 0
      add :category, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:user_tags, [:name], name: "user_tags_unique_tag_name_index")

    create table(:promoters, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :promoter_code, :text, null: false
      add :level, :bigint, null: false, default: 1
      add :status, :bigint, null: false, default: 2
      add :commission_rate, :decimal, null: false, default: "5.0"
      add :parent_promoter_id, :uuid
      add :total_commission, :decimal, null: false, default: "0"
      add :total_invites, :bigint, null: false, default: 0
      add :active_invites, :bigint, null: false, default: 0
      add :description, :text
      add :tags, {:array, :text}, default: []
      add :last_active_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:hundred_player_game_states, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :game_id, :bigint, null: false
      add :game_type, :text, null: false
      add :current_inventory, :decimal, null: false, default: "0"
      add :center_line, :decimal, null: false, default: "0"
      add :dark_tax_accumulated, :decimal, null: false, default: "0"
      add :last_result, :text

      add :last_update, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:hundred_player_game_states, [:game_id, :game_type],
             name: "hundred_player_game_states_unique_game_index"
           )

    create table(:weekly_cards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :title, :text, null: false
      add :recharge_amount, :decimal, null: false
      add :initial_reward, :decimal, null: false, default: "0"
      add :daily_reward, :decimal, null: false, default: "0"
      add :claim_days, :bigint, null: false, default: 7
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:weekly_cards, [:recharge_amount],
             name: "weekly_cards_unique_recharge_amount_index"
           )

    create table(:room_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :game_config_id, :uuid, null: false
      add :game_id, :bigint, null: false
      add :server_id, :bigint, null: false
      add :server_ip, :text, null: false
      add :port, :bigint, null: false
      add :order_id, :bigint, null: false
      add :min_bet, :bigint, null: false
      add :entry_fee, :bigint, null: false
      add :max_bet, :bigint, null: false, default: 0
      add :max_players, :bigint, null: false, default: 6
      add :bundle_name, :text, null: false
      add :is_enabled, :boolean, null: false, default: true
      add :basic_config, :map, default: %{}
      add :gameplay_config, :map, default: %{}
      add :unified_config, :map, default: %{}
      add :created_by, :uuid
      add :updated_by, :uuid

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:exchange_orders, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :order_id, :text, null: false
      add :user_id, :uuid, null: false
      add :platform, :text, null: false
      add :exchange_type, :bigint, null: false
      add :exchange_amount, :decimal, null: false
      add :current_coins, :decimal, null: false
      add :tax_amount, :decimal, null: false, default: "0"
      add :total_recharge, :decimal, null: false, default: "0"
      add :total_exchange, :decimal, null: false, default: "0"
      add :bank_info, :text
      add :alipay_info, :text
      add :audit_status, :bigint, null: false, default: 0
      add :progress_status, :bigint, null: false, default: 0
      add :result_status, :bigint, null: false, default: 0
      add :feedback, :text
      add :ip_address, :text
      add :auditor_id, :uuid
      add :audit_time, :utc_datetime
      add :process_time, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:promotion_settlements, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :promoter_id,
          references(:promoters,
            column: :id,
            name: "promotion_settlements_promoter_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :settlement_period, :text, null: false
      add :commission_amount, :decimal, null: false, default: "0"
      add :bonus_amount, :decimal, null: false, default: "0"
      add :deduction_amount, :decimal, null: false, default: "0"
      add :total_amount, :decimal, null: false, default: "0"
      add :status, :bigint, null: false, default: 0
      add :settlement_date, :date
      add :payment_date, :date
      add :payment_method, :text
      add :payment_reference, :text
      add :remarks, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:promotion_settlements, [:promoter_id, :settlement_period],
             name: "promotion_settlements_unique_promoter_period_index"
           )

    create table(:payment_orders, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :order_id, :text, null: false
      add :gateway_order_id, :text
      add :user_id, :uuid, null: false
      add :gateway_config_id, :uuid, null: false
      add :order_type, :text, null: false
      add :amount, :decimal, null: false
      add :currency, :text, null: false, default: "INR"
      add :status, :text, null: false, default: "pending"
      add :gateway_status, :text
      add :payment_method, :text
      add :payment_url, :text
      add :qr_code, :text
      add :customer_info, :map, null: false, default: %{}
      add :bank_info, :map, null: false, default: %{}
      add :gateway_request, :map, null: false, default: %{}
      add :gateway_response, :map, null: false, default: %{}
      add :callback_data, :map, null: false, default: %{}
      add :fee_amount, :decimal, null: false, default: "0"
      add :actual_amount, :decimal, null: false, default: "0"
      add :error_message, :text
      add :completed_at, :utc_datetime
      add :cancelled_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:recharge_wheels, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :cumulative_recharge, :decimal, null: false
      add :wheel_spins, :bigint, null: false, default: 1
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:recharge_wheels, [:cumulative_recharge],
             name: "recharge_wheels_unique_recharge_amount_index"
           )

    create table(:game_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:room_configs) do
      modify :game_config_id,
             references(:game_configs,
               column: :id,
               name: "room_configs_game_config_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:game_configs) do
      add :game_id, :bigint, null: false
      add :game_name, :text, null: false
      add :display_name, :text, null: false
      add :status, :bigint, null: false, default: 2
      add :mode, :text, null: false, default: ""
      add :icon_url, :text
      add :description, :text
      add :close_notice, :text
      add :display_order, :bigint, null: false, default: 0
      add :is_enabled, :boolean, null: false, default: true
      add :game_class_type, :bigint, null: false, default: 1
      add :created_by, :uuid
      add :updated_by, :uuid

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:promotion_channels, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :promoter_id,
          references(:promoters,
            column: :id,
            name: "promotion_channels_promoter_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :channel_name, :text, null: false
      add :channel_type, :text, null: false
      add :channel_url, :text
      add :qr_code_url, :text
      add :status, :bigint, null: false, default: 1
      add :click_count, :bigint, null: false, default: 0
      add :register_count, :bigint, null: false, default: 0
      add :conversion_rate, :decimal, null: false, default: "0"
      add :description, :text
      add :tags, {:array, :text}, default: []
      add :last_click_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:promotion_channels, [:promoter_id, :channel_name],
             name: "promotion_channels_unique_promoter_channel_index"
           )

    create table(:ledger_accounts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
    end

    alter table(:ledger_transfers) do
      modify :from_account_id,
             references(:ledger_accounts,
               column: :id,
               name: "ledger_transfers_from_account_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :to_account_id,
             references(:ledger_accounts,
               column: :id,
               name: "ledger_transfers_to_account_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:ledger_balances) do
      modify :account_id,
             references(:ledger_accounts,
               column: :id,
               name: "ledger_balances_account_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:ledger_balances, [:account_id, :transfer_id],
             name: "ledger_balances_unique_references_index"
           )

    alter table(:ledger_accounts) do
      add :identifier, :text, null: false
      add :currency, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :description, :text
      add :is_active, :boolean, null: false, default: true
    end

    create unique_index(:ledger_accounts, [:identifier],
             name: "ledger_accounts_unique_identifier_index"
           )

    create table(:user_game_statistics, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :current_win_streak, :bigint, null: false, default: 0
      add :max_win_streak, :bigint, null: false, default: 0
      add :total_loss_amount, :decimal, null: false, default: "0"
      add :cumulative_recharge, :decimal, null: false, default: "0"
      add :total_games_played, :bigint, null: false, default: 0
      add :total_wins, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_activity_participations, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :activity_type, :text, null: false
      add :activity_id, :uuid
      add :progress, :bigint, null: false, default: 0
      add :status, :text, null: false, default: "active"
      add :participation_data, :map
      add :participated_at, :utc_datetime, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:scratch_card_level_rewards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :reward_type, :text, null: false
      add :min_reward, :decimal, null: false
      add :max_reward, :decimal, null: false
      add :actual_max_reward, :decimal, null: false
      add :probability, :decimal, null: false
      add :sort_order, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :task_level_id,
          references(:scratch_card_task_levels,
            column: :id,
            name: "scratch_card_level_rewards_task_level_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create table(:piggy_banks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :bank_type, :text, null: false
      add :target_amount, :decimal, null: false
      add :current_amount, :decimal, null: false, default: "0"
      add :bonus_rate, :decimal, null: false, default: "1.0"
      add :unlock_condition, :map
      add :status, :text, null: false, default: "active"
      add :created_at, :utc_datetime, null: false
      add :last_deposit_at, :utc_datetime
      add :broken_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:customer_chats, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :platform, :text, null: false
      add :vip_level, :bigint, default: 0
      add :svip_level, :bigint, default: 0
      add :question, :text, null: false
      add :question_image, :text
      add :reply_content, :text
      add :status, :bigint, null: false, default: 0
      add :ip_address, :text
      add :processed_at, :utc_datetime
      add :customer_service_id, :uuid

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:recharge_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :order_id, :text, null: false
      add :external_order_id, :text
      add :amount, :decimal, null: false
      add :currency, :text, null: false, default: "XAA"
      add :payment_method, :text
      add :status, :text, null: false, default: "pending"
      add :callback_data, :map, default: %{}
      add :error_message, :text
      add :completed_at, :utc_datetime
      add :failed_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:jackpot_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :game_id, :text, null: false
      add :jackpot_id, :text, null: false
      add :name, :text, null: false
      add :description, :text
      add :base_amount, :bigint, null: false, default: 0
      add :current_balance, :bigint, null: false, default: 0
      add :min_amount, :bigint, null: false, default: 0
      add :max_amount, :bigint, null: false, default: 100_000_000
      add :contribution_rate, :decimal, null: false, default: "0.02"
      add :weight, :bigint, null: false, default: 1
      add :priority, :bigint, null: false, default: 1
      add :status, :text, null: false, default: "active"
      add :trigger_conditions, :map
      add :reset_on_win, :boolean, null: false, default: false
      add :auto_reset_threshold, :bigint
      add :last_initialized_at, :utc_datetime
      add :last_reset_at, :utc_datetime
      add :last_updated_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:jackpot_configs, [:game_id, :jackpot_id],
             name: "jackpot_configs_unique_game_jackpot_index"
           )

    create table(:binding_rewards, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :title, :text, null: false
      add :binding_type, :text, null: false
      add :reward_amount, :decimal, null: false
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:binding_rewards, [:binding_type],
             name: "binding_rewards_unique_binding_type_index"
           )

    create table(:user_luck_values, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :current_luck, :bigint, default: -1
      add :last_updated_at, :utc_datetime_usec

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:seven_day_tasks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :day_number, :bigint, null: false
      add :reward_amount, :decimal, null: false, default: "0"
      add :is_cyclic, :boolean, null: false, default: true
      add :status, :text, null: false, default: "enabled"

      add :description, :text
      add :is_special, :boolean, null: false, default: false
      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:seven_day_tasks, [:day_number],
             name: "seven_day_tasks_unique_day_number_index"
           )

    create table(:user_questions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :title, :text, null: false
      add :content, :text, null: false
      add :question_type, :bigint, null: false
      add :priority, :bigint, null: false, default: 2
      add :status, :bigint, null: false, default: 0
      add :contact_phone, :text
      add :contact_email, :text
      add :assigned_staff_id, :uuid
      add :completed_at, :utc_datetime
      add :images, {:array, :text}, default: []

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:withdrawal_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :order_id, :text, null: false
      add :user_id, :uuid, null: false
      add :platform, :text, null: false, default: "web"
      add :withdrawal_amount, :decimal, null: false
      add :payment_method, :text, null: false
      add :current_balance, :decimal, null: false
      add :withdrawable_balance, :decimal, null: false
      add :required_turnover, :decimal, null: false, default: "0"
      add :completed_turnover, :decimal, null: false, default: "0"
      add :fee_amount, :decimal, null: false, default: "0"
      add :tax_amount, :decimal, null: false, default: "0"
      add :actual_amount, :decimal, null: false
      add :bank_info, :text
      add :alipay_info, :text
      add :upi_info, :text
      add :audit_status, :bigint, null: false, default: 0
      add :progress_status, :bigint, null: false, default: 0
      add :result_status, :bigint, null: false, default: 0
      add :gateway_order_id, :text
      add :gateway_response, :text
      add :feedback, :text
      add :ip_address, :text
      add :auditor_id, :uuid
      add :audit_time, :utc_datetime
      add :process_time, :utc_datetime
      add :completed_time, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:game_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :game_id, :text, null: false
      add :game_type, :text, null: false
      add :bet_amount, :decimal, null: false
      add :win_amount, :decimal, null: false, default: "0"
      add :result_status, :text, null: false
      add :game_data, :map, default: %{}
      add :duration, :bigint
      add :started_at, :utc_datetime
      add :completed_at, :utc_datetime

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:api_keys, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :api_key_hash, :binary, null: false
      add :expires_at, :utc_datetime_usec, null: false
      add :user_id, :uuid
    end

    create table(:system_reports, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :report_type, :text, null: false
      add :report_date, :date, null: false
      add :total_users, :bigint, null: false, default: 0
      add :active_users, :bigint, null: false, default: 0
      add :new_users, :bigint, null: false, default: 0
      add :revenue, :decimal, null: false, default: "0"
      add :total_recharge, :decimal, null: false, default: "0"
      add :total_exchange, :decimal, null: false, default: "0"
      add :game_rounds, :bigint, null: false, default: 0
      add :online_peak, :bigint, null: false, default: 0
      add :retention_rate, :decimal
      add :report_data, :map

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:system_reports, [:report_type, :report_date],
             name: "system_reports_unique_report_type_date_index"
           )

    create table(:invite_reward_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :round_number, :bigint, null: false
      add :reward_type, :text, null: false
      add :min_reward, :decimal, null: false
      add :max_reward, :decimal, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :activity_id, :uuid
    end

    create table(:free_bonus_tasks, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :title, :text, null: false
      add :share_count, :bigint, null: false, default: 1
      add :game_id, :text
      add :game_name, :text
      add :required_win_coins, :decimal, null: false, default: "0"
      add :withdraw_count, :bigint, null: false, default: 1
      add :reward_amount, :decimal, null: false, default: "0"
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:user_profiles, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :nickname, :text
      add :gender, :bigint
      add :avatar_url, :text
      add :head_id, :bigint

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:racing_game_bets, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :race_issue, :text, null: false
      add :selection, :text, null: false
      add :amount, :bigint, null: false
      add :status, :bigint, null: false, default: 0
      add :payout, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:users, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:user_turnovers) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_turnovers_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_turnovers, [:user_id], name: "user_turnovers_unique_user_index")

    alter table(:cdkey_batches) do
      modify :creator_id,
             references(:users,
               column: :id,
               name: "cdkey_batches_creator_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :template_id,
             references(:cdkey_templates,
               column: :id,
               name: "cdkey_batches_template_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:cdkey_batches, [:batch_name],
             name: "cdkey_batches_unique_batch_name_index"
           )

    alter table(:reward_claim_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "reward_claim_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:agent_relationships) do
      modify :agent_id,
             references(:users,
               column: :id,
               name: "agent_relationships_agent_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :subordinate_id,
             references(:users,
               column: :id,
               name: "agent_relationships_subordinate_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:agent_relationships, [:agent_id, :subordinate_id],
             name: "agent_relationships_unique_agent_subordinate_index"
           )

    alter table(:weekly_card_purchases) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "weekly_card_purchases_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :card_id,
             references(:weekly_cards,
               column: :id,
               name: "weekly_card_purchases_card_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:user_activity_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_activity_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_activity_records, [:user_id, :activity_type, :activity_id],
             name: "user_activity_records_unique_user_activity_index"
           )

    alter table(:user_bans) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_bans_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :operator_id,
             references(:users,
               column: :id,
               name: "user_bans_operator_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :unban_operator_id,
             references(:users,
               column: :id,
               name: "user_bans_unban_operator_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_bans, [:user_id, :status],
             name: "user_bans_unique_active_user_ban_index",
             where: "(status = 1)"
           )

    alter table(:user_rewards) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_rewards_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:users_versions) do
      modify :version_source_id,
             references(:users,
               column: :id,
               name: "users_versions_version_source_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:user_bank_cards) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_bank_cards_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :bank_id,
             references(:bank_configs,
               column: :id,
               name: "user_bank_cards_bank_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_bank_cards, [:user_id, :account_number],
             name: "user_bank_cards_unique_user_account_index"
           )

    alter table(:cdkey_activities) do
      modify :used_by_user_id,
             references(:users,
               column: :id,
               name: "cdkey_activities_used_by_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:cdkey_activities, [:code], name: "cdkey_activities_unique_code_index")

    alter table(:user_login_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_login_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_login_records, [:user_id, :login_date, :is_first_today],
             name: "user_login_records_unique_user_date_first_index",
             where: "(is_first_today = true)"
           )

    alter table(:user_identities) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_identities_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_identities, [:strategy, :uid, :user_id],
             name: "user_identities_unique_on_strategy_and_uid_and_user_id_index"
           )

    alter table(:cdkey_claim_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "cdkey_claim_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :cdkey_id,
             references(:cdkey_activities,
               column: :id,
               name: "cdkey_claim_records_cdkey_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:cdkey_claim_records, [:user_id, :cdkey_id],
             name: "cdkey_claim_records_unique_user_cdkey_index"
           )

    alter table(:user_vip_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_vip_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_vip_records, [:user_id],
             name: "user_vip_records_unique_user_vip_record_index"
           )

    alter table(:share_settlements) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "share_settlements_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(
             :share_settlements,
             [:user_id, :share_id, :settlement_type, :settlement_period],
             name: "share_settlements_unique_user_share_type_period_index"
           )

    alter table(:user_vip_infos) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_vip_infos_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_vip_infos, [:user_id], name: "user_vip_infos_unique_user_index")

    alter table(:user_purchases) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_purchases_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :product_id,
             references(:products,
               column: :id,
               name: "user_purchases_product_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:racing_game_stocks) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "racing_game_stocks_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:racing_game_stocks, [:user_id, :racer_id],
             name: "racing_game_stocks_unique_user_stock_index"
           )

    alter table(:promoters) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "promoters_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :parent_promoter_id,
             references(:promoters,
               column: :id,
               name: "promoters_parent_promoter_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:promoters, [:promoter_code],
             name: "promoters_unique_promoter_code_index"
           )

    create unique_index(:promoters, [:user_id], name: "promoters_unique_user_id_index")

    alter table(:room_configs) do
      modify :created_by,
             references(:users,
               column: :id,
               name: "room_configs_created_by_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :updated_by,
             references(:users,
               column: :id,
               name: "room_configs_updated_by_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:room_configs, [:game_id, :server_id],
             name: "room_configs_unique_game_server_index"
           )

    alter table(:exchange_orders) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "exchange_orders_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :auditor_id,
             references(:users,
               column: :id,
               name: "exchange_orders_auditor_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:exchange_orders, [:order_id],
             name: "exchange_orders_unique_order_id_index"
           )

    alter table(:payment_orders) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "payment_orders_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :gateway_config_id,
             references(:payment_gateway,
               column: :id,
               name: "payment_orders_gateway_config_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:payment_orders, [:order_id],
             name: "payment_orders_unique_order_id_index"
           )

    alter table(:game_configs) do
      modify :created_by,
             references(:users,
               column: :id,
               name: "game_configs_created_by_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :updated_by,
             references(:users,
               column: :id,
               name: "game_configs_updated_by_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:game_configs, [:game_id], name: "game_configs_unique_game_id_index")

    alter table(:wallet_control_configs) do
      modify :game_id,
             references(:game_configs,
               column: :game_id,
               name: "wallet_control_configs_game_id_fkey",
               type: :bigint,
               prefix: "public"
             )

      modify :created_by,
             references(:users,
               column: :id,
               name: "wallet_control_configs_created_by_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :updated_by,
             references(:users,
               column: :id,
               name: "wallet_control_configs_updated_by_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:wallet_control_configs, [:game_id],
             name: "wallet_control_configs_unique_game_id_index"
           )

    alter table(:user_game_statistics) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_game_statistics_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_game_statistics, [:user_id],
             name: "user_game_statistics_unique_user_index"
           )

    alter table(:user_activity_participations) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_activity_participations_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_activity_participations, [:user_id, :activity_type, :activity_id],
             name: "user_activity_participations_unique_user_activity_index"
           )

    alter table(:piggy_banks) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "piggy_banks_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:customer_chats) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "customer_chats_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :customer_service_id,
             references(:users,
               column: :id,
               name: "customer_chats_customer_service_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:customer_chats, [:user_id, :inserted_at],
             name: "customer_chats_unique_user_question_time_index"
           )

    alter table(:recharge_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "recharge_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:recharge_records, [:order_id],
             name: "recharge_records_unique_order_id_index"
           )

    alter table(:user_luck_values) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_luck_values_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_luck_values, [:user_id],
             name: "user_luck_values_unique_user_id_index"
           )

    alter table(:user_questions) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_questions_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :assigned_staff_id,
             references(:users,
               column: :id,
               name: "user_questions_assigned_staff_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:withdrawal_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "withdrawal_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :auditor_id,
             references(:users,
               column: :id,
               name: "withdrawal_records_auditor_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:withdrawal_records, [:order_id],
             name: "withdrawal_records_unique_order_id_index"
           )

    alter table(:game_records) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "game_records_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:api_keys) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "api_keys_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")

    alter table(:user_profiles) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "user_profiles_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:user_profiles, [:user_id],
             name: "user_profiles_unique_user_profile_index"
           )

    alter table(:racing_game_bets) do
      modify :user_id,
             references(:users,
               column: :id,
               name: "racing_game_bets_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:users) do
      add :numeric_id, :bigint, null: false
      add :username, :citext
      add :email, :citext
      add :phone, :text
      add :phone_verified_at, :utc_datetime
      add :last_offline_at, :utc_datetime
      add :hashed_password, :text, null: false
      add :confirmed_at, :utc_datetime_usec
      add :agent_level, :bigint, null: false, default: -1
      add :permission_level, :bigint, null: false, default: 0

      add :channel_id,
          references(:channels,
            column: :channel_id,
            name: "users_channel_id_fkey",
            type: :text,
            prefix: "public"
          )

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:users, [:username], name: "users_global_username_index")

    create unique_index(:users, [:channel_id, :email], name: "users_unique_email_index")

    create unique_index(:users, [:channel_id, :numeric_id], name: "users_unique_numeric_id_index")

    create unique_index(:users, [:channel_id, :phone], name: "users_unique_phone_index")

    create table(:user_devices, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :user_id,
          references(:users,
            column: :id,
            name: "user_devices_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :device_id, :text, null: false
      add :last_login_ip, :text
      add :last_login_at, :utc_datetime
      add :last_offline_at, :utc_datetime
      add :device_info, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:user_devices, [:user_id, :device_id],
             name: "user_devices_unique_user_device_index"
           )

    create table(:invite_cash_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:invite_reward_configs) do
      modify :activity_id,
             references(:invite_cash_activities,
               column: :id,
               name: "invite_reward_configs_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:invite_reward_configs, [:activity_id, :round_number],
             name: "invite_reward_configs_unique_activity_round_index"
           )

    alter table(:invite_cash_activities) do
      add :title, :text, null: false
      add :total_reward, :decimal, null: false
      add :initial_min, :decimal, null: false
      add :initial_max, :decimal, null: false
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:sensitive_words, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :keyword, :text, null: false
      add :status, :bigint, null: false, default: 1
      add :category, :text
      add :severity, :bigint, null: false, default: 1
      add :action_type, :bigint, null: false, default: 2
      add :replacement, :text
      add :description, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:sensitive_words, [:keyword],
             name: "sensitive_words_unique_keyword_index"
           )

    create table(:login_cash_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :reward_type, :text, null: false, default: "cash"
      add :total_reward, :decimal, null: false, default: "0"
      add :task_config, :map, null: false, default: %{}
      add :reward_config, :map, null: false, default: %{}
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    drop table(:login_cash_activities)

    drop_if_exists unique_index(:sensitive_words, [:keyword],
                     name: "sensitive_words_unique_keyword_index"
                   )

    drop table(:sensitive_words)

    alter table(:invite_cash_activities) do
      remove :updated_at
      remove :inserted_at
      remove :status
      remove :initial_max
      remove :initial_min
      remove :total_reward
      remove :title
    end

    drop_if_exists unique_index(:invite_reward_configs, [:activity_id, :round_number],
                     name: "invite_reward_configs_unique_activity_round_index"
                   )

    drop constraint(:invite_reward_configs, "invite_reward_configs_activity_id_fkey")

    alter table(:invite_reward_configs) do
      modify :activity_id, :uuid
    end

    drop table(:invite_cash_activities)

    drop_if_exists unique_index(:user_devices, [:user_id, :device_id],
                     name: "user_devices_unique_user_device_index"
                   )

    drop constraint(:user_devices, "user_devices_user_id_fkey")

    drop table(:user_devices)

    drop_if_exists unique_index(:users, [:channel_id, :phone], name: "users_unique_phone_index")

    drop_if_exists unique_index(:users, [:channel_id, :numeric_id],
                     name: "users_unique_numeric_id_index"
                   )

    drop_if_exists unique_index(:users, [:channel_id, :email], name: "users_unique_email_index")

    drop_if_exists unique_index(:users, [:username], name: "users_global_username_index")

    drop constraint(:users, "users_channel_id_fkey")

    alter table(:users) do
      remove :updated_at
      remove :inserted_at
      remove :channel_id
      remove :permission_level
      remove :agent_level
      remove :confirmed_at
      remove :hashed_password
      remove :last_offline_at
      remove :phone_verified_at
      remove :phone
      remove :email
      remove :username
      remove :numeric_id
    end

    drop constraint(:racing_game_bets, "racing_game_bets_user_id_fkey")

    alter table(:racing_game_bets) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_profiles, [:user_id],
                     name: "user_profiles_unique_user_profile_index"
                   )

    drop constraint(:user_profiles, "user_profiles_user_id_fkey")

    alter table(:user_profiles) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")

    drop constraint(:api_keys, "api_keys_user_id_fkey")

    alter table(:api_keys) do
      modify :user_id, :uuid
    end

    drop constraint(:game_records, "game_records_user_id_fkey")

    alter table(:game_records) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:withdrawal_records, [:order_id],
                     name: "withdrawal_records_unique_order_id_index"
                   )

    drop constraint(:withdrawal_records, "withdrawal_records_user_id_fkey")

    drop constraint(:withdrawal_records, "withdrawal_records_auditor_id_fkey")

    alter table(:withdrawal_records) do
      modify :auditor_id, :uuid
      modify :user_id, :uuid
    end

    drop constraint(:user_questions, "user_questions_user_id_fkey")

    drop constraint(:user_questions, "user_questions_assigned_staff_id_fkey")

    alter table(:user_questions) do
      modify :assigned_staff_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_luck_values, [:user_id],
                     name: "user_luck_values_unique_user_id_index"
                   )

    drop constraint(:user_luck_values, "user_luck_values_user_id_fkey")

    alter table(:user_luck_values) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:recharge_records, [:order_id],
                     name: "recharge_records_unique_order_id_index"
                   )

    drop constraint(:recharge_records, "recharge_records_user_id_fkey")

    alter table(:recharge_records) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:customer_chats, [:user_id, :inserted_at],
                     name: "customer_chats_unique_user_question_time_index"
                   )

    drop constraint(:customer_chats, "customer_chats_user_id_fkey")

    drop constraint(:customer_chats, "customer_chats_customer_service_id_fkey")

    alter table(:customer_chats) do
      modify :customer_service_id, :uuid
      modify :user_id, :uuid
    end

    drop constraint(:piggy_banks, "piggy_banks_user_id_fkey")

    alter table(:piggy_banks) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(
                     :user_activity_participations,
                     [:user_id, :activity_type, :activity_id],
                     name: "user_activity_participations_unique_user_activity_index"
                   )

    drop constraint(:user_activity_participations, "user_activity_participations_user_id_fkey")

    alter table(:user_activity_participations) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_game_statistics, [:user_id],
                     name: "user_game_statistics_unique_user_index"
                   )

    drop constraint(:user_game_statistics, "user_game_statistics_user_id_fkey")

    alter table(:user_game_statistics) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:wallet_control_configs, [:game_id],
                     name: "wallet_control_configs_unique_game_id_index"
                   )

    drop constraint(:wallet_control_configs, "wallet_control_configs_game_id_fkey")

    drop constraint(:wallet_control_configs, "wallet_control_configs_created_by_fkey")

    drop constraint(:wallet_control_configs, "wallet_control_configs_updated_by_fkey")

    alter table(:wallet_control_configs) do
      modify :updated_by, :uuid
      modify :created_by, :uuid
      modify :game_id, :bigint
    end

    drop_if_exists unique_index(:game_configs, [:game_id],
                     name: "game_configs_unique_game_id_index"
                   )

    drop constraint(:game_configs, "game_configs_created_by_fkey")

    drop constraint(:game_configs, "game_configs_updated_by_fkey")

    alter table(:game_configs) do
      modify :updated_by, :uuid
      modify :created_by, :uuid
    end

    drop_if_exists unique_index(:payment_orders, [:order_id],
                     name: "payment_orders_unique_order_id_index"
                   )

    drop constraint(:payment_orders, "payment_orders_user_id_fkey")

    drop constraint(:payment_orders, "payment_orders_gateway_config_id_fkey")

    alter table(:payment_orders) do
      modify :gateway_config_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:exchange_orders, [:order_id],
                     name: "exchange_orders_unique_order_id_index"
                   )

    drop constraint(:exchange_orders, "exchange_orders_user_id_fkey")

    drop constraint(:exchange_orders, "exchange_orders_auditor_id_fkey")

    alter table(:exchange_orders) do
      modify :auditor_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:room_configs, [:game_id, :server_id],
                     name: "room_configs_unique_game_server_index"
                   )

    drop constraint(:room_configs, "room_configs_created_by_fkey")

    drop constraint(:room_configs, "room_configs_updated_by_fkey")

    alter table(:room_configs) do
      modify :updated_by, :uuid
      modify :created_by, :uuid
    end

    drop_if_exists unique_index(:promoters, [:user_id], name: "promoters_unique_user_id_index")

    drop_if_exists unique_index(:promoters, [:promoter_code],
                     name: "promoters_unique_promoter_code_index"
                   )

    drop constraint(:promoters, "promoters_user_id_fkey")

    drop constraint(:promoters, "promoters_parent_promoter_id_fkey")

    alter table(:promoters) do
      modify :parent_promoter_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:racing_game_stocks, [:user_id, :racer_id],
                     name: "racing_game_stocks_unique_user_stock_index"
                   )

    drop constraint(:racing_game_stocks, "racing_game_stocks_user_id_fkey")

    alter table(:racing_game_stocks) do
      modify :user_id, :uuid
    end

    drop constraint(:user_purchases, "user_purchases_user_id_fkey")

    drop constraint(:user_purchases, "user_purchases_product_id_fkey")

    alter table(:user_purchases) do
      modify :product_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_vip_infos, [:user_id],
                     name: "user_vip_infos_unique_user_index"
                   )

    drop constraint(:user_vip_infos, "user_vip_infos_user_id_fkey")

    alter table(:user_vip_infos) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(
                     :share_settlements,
                     [:user_id, :share_id, :settlement_type, :settlement_period],
                     name: "share_settlements_unique_user_share_type_period_index"
                   )

    drop constraint(:share_settlements, "share_settlements_user_id_fkey")

    alter table(:share_settlements) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_vip_records, [:user_id],
                     name: "user_vip_records_unique_user_vip_record_index"
                   )

    drop constraint(:user_vip_records, "user_vip_records_user_id_fkey")

    alter table(:user_vip_records) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:cdkey_claim_records, [:user_id, :cdkey_id],
                     name: "cdkey_claim_records_unique_user_cdkey_index"
                   )

    drop constraint(:cdkey_claim_records, "cdkey_claim_records_user_id_fkey")

    drop constraint(:cdkey_claim_records, "cdkey_claim_records_cdkey_id_fkey")

    alter table(:cdkey_claim_records) do
      modify :cdkey_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_identities, [:strategy, :uid, :user_id],
                     name: "user_identities_unique_on_strategy_and_uid_and_user_id_index"
                   )

    drop constraint(:user_identities, "user_identities_user_id_fkey")

    alter table(:user_identities) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_login_records, [:user_id, :login_date, :is_first_today],
                     name: "user_login_records_unique_user_date_first_index"
                   )

    drop constraint(:user_login_records, "user_login_records_user_id_fkey")

    alter table(:user_login_records) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:cdkey_activities, [:code],
                     name: "cdkey_activities_unique_code_index"
                   )

    drop constraint(:cdkey_activities, "cdkey_activities_used_by_user_id_fkey")

    alter table(:cdkey_activities) do
      modify :used_by_user_id, :uuid
    end

    drop_if_exists unique_index(:user_bank_cards, [:user_id, :account_number],
                     name: "user_bank_cards_unique_user_account_index"
                   )

    drop constraint(:user_bank_cards, "user_bank_cards_user_id_fkey")

    drop constraint(:user_bank_cards, "user_bank_cards_bank_id_fkey")

    alter table(:user_bank_cards) do
      modify :bank_id, :uuid
      modify :user_id, :uuid
    end

    drop constraint(:users_versions, "users_versions_version_source_id_fkey")

    alter table(:users_versions) do
      modify :version_source_id, :uuid
    end

    drop constraint(:user_rewards, "user_rewards_user_id_fkey")

    alter table(:user_rewards) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_bans, [:user_id, :status],
                     name: "user_bans_unique_active_user_ban_index"
                   )

    drop constraint(:user_bans, "user_bans_user_id_fkey")

    drop constraint(:user_bans, "user_bans_operator_id_fkey")

    drop constraint(:user_bans, "user_bans_unban_operator_id_fkey")

    alter table(:user_bans) do
      modify :unban_operator_id, :uuid
      modify :operator_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:user_activity_records, [:user_id, :activity_type, :activity_id],
                     name: "user_activity_records_unique_user_activity_index"
                   )

    drop constraint(:user_activity_records, "user_activity_records_user_id_fkey")

    alter table(:user_activity_records) do
      modify :user_id, :uuid
    end

    drop constraint(:weekly_card_purchases, "weekly_card_purchases_user_id_fkey")

    drop constraint(:weekly_card_purchases, "weekly_card_purchases_card_id_fkey")

    alter table(:weekly_card_purchases) do
      modify :card_id, :uuid
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:agent_relationships, [:agent_id, :subordinate_id],
                     name: "agent_relationships_unique_agent_subordinate_index"
                   )

    drop constraint(:agent_relationships, "agent_relationships_agent_id_fkey")

    drop constraint(:agent_relationships, "agent_relationships_subordinate_id_fkey")

    alter table(:agent_relationships) do
      modify :subordinate_id, :uuid
      modify :agent_id, :uuid
    end

    drop constraint(:reward_claim_records, "reward_claim_records_user_id_fkey")

    alter table(:reward_claim_records) do
      modify :user_id, :uuid
    end

    drop_if_exists unique_index(:cdkey_batches, [:batch_name],
                     name: "cdkey_batches_unique_batch_name_index"
                   )

    drop constraint(:cdkey_batches, "cdkey_batches_creator_id_fkey")

    drop constraint(:cdkey_batches, "cdkey_batches_template_id_fkey")

    alter table(:cdkey_batches) do
      modify :template_id, :uuid
      modify :creator_id, :uuid
    end

    drop_if_exists unique_index(:user_turnovers, [:user_id],
                     name: "user_turnovers_unique_user_index"
                   )

    drop constraint(:user_turnovers, "user_turnovers_user_id_fkey")

    alter table(:user_turnovers) do
      modify :user_id, :uuid
    end

    drop table(:users)

    drop table(:racing_game_bets)

    drop table(:user_profiles)

    drop table(:free_bonus_tasks)

    drop table(:invite_reward_configs)

    drop_if_exists unique_index(:system_reports, [:report_type, :report_date],
                     name: "system_reports_unique_report_type_date_index"
                   )

    drop table(:system_reports)

    drop table(:api_keys)

    drop table(:game_records)

    drop table(:withdrawal_records)

    drop table(:user_questions)

    drop_if_exists unique_index(:seven_day_tasks, [:day_number],
                     name: "seven_day_tasks_unique_day_number_index"
                   )

    drop table(:seven_day_tasks)

    drop table(:user_luck_values)

    drop_if_exists unique_index(:binding_rewards, [:binding_type],
                     name: "binding_rewards_unique_binding_type_index"
                   )

    drop table(:binding_rewards)

    drop_if_exists unique_index(:jackpot_configs, [:game_id, :jackpot_id],
                     name: "jackpot_configs_unique_game_jackpot_index"
                   )

    drop table(:jackpot_configs)

    drop table(:recharge_records)

    drop table(:customer_chats)

    drop table(:piggy_banks)

    drop constraint(:scratch_card_level_rewards, "scratch_card_level_rewards_task_level_id_fkey")

    drop table(:scratch_card_level_rewards)

    drop table(:user_activity_participations)

    drop table(:user_game_statistics)

    drop_if_exists unique_index(:ledger_accounts, [:identifier],
                     name: "ledger_accounts_unique_identifier_index"
                   )

    alter table(:ledger_accounts) do
      remove :is_active
      remove :description
      remove :updated_at
      remove :inserted_at
      remove :currency
      remove :identifier
    end

    drop_if_exists unique_index(:ledger_balances, [:account_id, :transfer_id],
                     name: "ledger_balances_unique_references_index"
                   )

    drop constraint(:ledger_balances, "ledger_balances_account_id_fkey")

    alter table(:ledger_balances) do
      modify :account_id, :uuid
    end

    drop constraint(:ledger_transfers, "ledger_transfers_from_account_id_fkey")

    drop constraint(:ledger_transfers, "ledger_transfers_to_account_id_fkey")

    alter table(:ledger_transfers) do
      modify :to_account_id, :uuid
      modify :from_account_id, :uuid
    end

    drop table(:ledger_accounts)

    drop_if_exists unique_index(:promotion_channels, [:promoter_id, :channel_name],
                     name: "promotion_channels_unique_promoter_channel_index"
                   )

    drop constraint(:promotion_channels, "promotion_channels_promoter_id_fkey")

    drop table(:promotion_channels)

    alter table(:game_configs) do
      remove :updated_at
      remove :inserted_at
      remove :updated_by
      remove :created_by
      remove :game_class_type
      remove :is_enabled
      remove :display_order
      remove :close_notice
      remove :description
      remove :icon_url
      remove :mode
      remove :status
      remove :display_name
      remove :game_name
      remove :game_id
    end

    drop constraint(:room_configs, "room_configs_game_config_id_fkey")

    alter table(:room_configs) do
      modify :game_config_id, :uuid
    end

    drop table(:game_configs)

    drop_if_exists unique_index(:recharge_wheels, [:cumulative_recharge],
                     name: "recharge_wheels_unique_recharge_amount_index"
                   )

    drop table(:recharge_wheels)

    drop table(:payment_orders)

    drop_if_exists unique_index(:promotion_settlements, [:promoter_id, :settlement_period],
                     name: "promotion_settlements_unique_promoter_period_index"
                   )

    drop constraint(:promotion_settlements, "promotion_settlements_promoter_id_fkey")

    drop table(:promotion_settlements)

    drop table(:exchange_orders)

    drop table(:room_configs)

    drop_if_exists unique_index(:weekly_cards, [:recharge_amount],
                     name: "weekly_cards_unique_recharge_amount_index"
                   )

    drop table(:weekly_cards)

    drop_if_exists unique_index(:hundred_player_game_states, [:game_id, :game_type],
                     name: "hundred_player_game_states_unique_game_index"
                   )

    drop table(:hundred_player_game_states)

    drop table(:promoters)

    drop_if_exists unique_index(:user_tags, [:name], name: "user_tags_unique_tag_name_index")

    drop table(:user_tags)

    drop table(:loss_rebate_jars)

    drop table(:free_cash_activities)

    drop constraint(:ledger_balances, "ledger_balances_transfer_id_fkey")

    drop table(:ledger_balances)

    drop table(:racing_game_stocks)

    drop_if_exists unique_index(:role_permissions, [:role_id, :permission_id],
                     name: "role_permissions_unique_role_permission_index"
                   )

    drop constraint(:role_permissions, "role_permissions_role_id_fkey")

    drop constraint(:role_permissions, "role_permissions_permission_id_fkey")

    drop table(:role_permissions)

    drop table(:operation_logs)

    drop table(:user_purchases)

    drop table(:wheel_prize_configs)

    drop_if_exists unique_index(:payment_configs, [:gateway_id, :payment_type],
                     name: "payment_configs_unique_gateway_payment_type_index"
                   )

    drop constraint(:payment_configs, "payment_configs_gateway_id_fkey")

    drop table(:payment_configs)

    drop_if_exists unique_index(:alert_records, [:alert_time, :alert_type],
                     name: "alert_records_unique_alert_time_type_index"
                   )

    drop table(:alert_records)

    drop table(:user_vip_infos)

    drop_if_exists unique_index(:roles, [:name], name: "roles_unique_name_index")

    drop_if_exists unique_index(:roles, [:code], name: "roles_unique_code_index")

    drop table(:roles)

    drop_if_exists unique_index(:permissions, [:code], name: "permissions_unique_code_index")

    drop constraint(:permissions, "permissions_parent_id_fkey")

    drop table(:permissions)

    drop_if_exists unique_index(:robot_entities, [:robot_id],
                     name: "robot_entities_unique_robot_id_index"
                   )

    drop_if_exists index(:robot_entities, [:status])

    drop_if_exists index(:robot_entities, [:is_enabled])

    drop_if_exists index(:robot_entities, [:current_game_type])

    drop_if_exists index(:robot_entities, [:current_room_id])

    drop_if_exists index(:robot_entities, [:status, :is_enabled])

    drop table(:robot_entities)

    drop table(:share_settlements)

    drop table(:user_vip_records)

    drop table(:cdkey_claim_records)

    drop table(:user_identities)

    drop_if_exists unique_index(:scratch_card_task_rounds, [:activity_id, :round_number],
                     name: "scratch_card_task_rounds_unique_activity_round_index"
                   )

    drop constraint(:scratch_card_task_rounds, "scratch_card_task_rounds_activity_id_fkey")

    drop table(:scratch_card_task_rounds)

    drop_if_exists unique_index(:cdkey_templates, [:name],
                     name: "cdkey_templates_unique_name_index"
                   )

    drop table(:cdkey_templates)

    drop table(:user_login_records)

    drop table(:wallet_control_configs)

    drop table(:ledger_transfers)

    drop_if_exists unique_index(:verification_codes, [:phone_number, :code, :inserted_at],
                     name: "verification_codes_unique_phone_code_time_index"
                   )

    drop table(:verification_codes)

    drop table(:first_recharge_gifts)

    drop table(:cdkey_activities)

    drop table(:user_bank_cards)

    drop_if_exists unique_index(:products, [:sku], name: "products_unique_sku_index")

    drop table(:products)

    drop_if_exists unique_index(:recharge_tasks, [:recharge_amount],
                     name: "recharge_tasks_unique_recharge_amount_index"
                   )

    drop table(:recharge_tasks)

    drop_if_exists unique_index(:platforms, [:platform_number],
                     name: "platforms_unique_platform_number_index"
                   )

    drop_if_exists unique_index(:platforms, [:platform_name],
                     name: "platforms_unique_platform_name_index"
                   )

    drop table(:platforms)

    drop table(:users_versions)

    drop table(:races)

    drop table(:user_rewards)

    drop table(:broke_award_activities)

    drop_if_exists unique_index(:payment_gateway, [:gateway_code],
                     name: "payment_gateway_unique_gateway_code_index"
                   )

    drop table(:payment_gateway)

    drop_if_exists unique_index(:product_templates, [:product_type, :is_default],
                     name: "product_templates_unique_default_per_type_index"
                   )

    drop table(:product_templates)

    drop table(:user_bans)

    drop_if_exists unique_index(:ip_whitelists, [:ip_range, :type],
                     name: "ip_whitelists_unique_ip_range_index"
                   )

    drop_if_exists unique_index(:ip_whitelists, [:ip_address, :type],
                     name: "ip_whitelists_unique_ip_address_index"
                   )

    drop table(:ip_whitelists)

    alter table(:scratch_card_activities) do
      remove :updated_at
      remove :inserted_at
      remove :status
      remove :reward_probability
      remove :claimable_count
      remove :activity_title
    end

    drop_if_exists unique_index(:scratch_card_task_levels, [:activity_id, :task_level],
                     name: "scratch_card_task_levels_unique_activity_level_index"
                   )

    drop constraint(:scratch_card_task_levels, "scratch_card_task_levels_activity_id_fkey")

    alter table(:scratch_card_task_levels) do
      modify :activity_id, :uuid
    end

    drop table(:scratch_card_activities)

    drop_if_exists unique_index(:vip_gifts, [:vip_level],
                     name: "vip_gifts_unique_vip_level_index"
                   )

    drop table(:vip_gifts)

    drop_if_exists unique_index(:channels, [:package_name],
                     name: "channels_unique_package_name_index"
                   )

    drop_if_exists unique_index(:channels, [:channel_id],
                     name: "channels_unique_channel_id_index"
                   )

    drop table(:channels)

    drop_if_exists unique_index(:vip_levels, [:level], name: "vip_levels_unique_level_index")

    drop table(:vip_levels)

    drop_if_exists unique_index(:game_tasks, [:game_id, :task_type],
                     name: "game_tasks_unique_game_task_index"
                   )

    drop table(:game_tasks)

    drop table(:tokens)

    drop table(:user_activity_records)

    drop table(:weekly_card_purchases)

    drop table(:agent_relationships)

    drop table(:reward_claim_records)

    drop_if_exists unique_index(:withdrawal_configs, [:payment_method, :config_name],
                     name: "withdrawal_configs_unique_payment_method_config_index"
                   )

    drop_if_exists unique_index(:withdrawal_configs, [:config_name],
                     name: "withdrawal_configs_unique_config_name_index"
                   )

    drop table(:withdrawal_configs)

    drop_if_exists unique_index(:bank_configs, [:bank_code],
                     name: "bank_configs_unique_bank_code_index"
                   )

    drop table(:bank_configs)

    drop_if_exists unique_index(:sign_in_activities, [:activity_name],
                     name: "sign_in_activities_unique_activity_name_index"
                   )

    drop table(:sign_in_activities)

    drop table(:cdkey_batches)

    drop table(:scratch_card_task_levels)

    drop table(:user_turnovers)

    drop_if_exists unique_index(:share_configs, [:config_key],
                     name: "share_configs_unique_config_key_index"
                   )

    drop table(:share_configs)

    drop_if_exists unique_index(:game_control_configs, [:game_id, :game_type],
                     name: "game_control_configs_unique_game_and_type_index"
                   )

    drop table(:game_control_configs)
  end
end
