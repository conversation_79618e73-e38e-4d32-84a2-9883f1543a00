defmodule Cypridina.Repo.Migrations.CreateReportSystemTables do
  @moduledoc """
  创建报表系统相关表
  """
  use Ecto.Migration

  def up do
    # 创建日报表
    create table(:daily_reports, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :report_date, :date, null: false
      add :platform_id, :uuid, null: true

      # 用户相关指标
      add :new_devices, :bigint, null: false, default: 0
      add :new_registrations, :bigint, null: false, default: 0
      add :effective_new_users, :bigint, null: false, default: 0
      add :mobile_registrations, :bigint, null: false, default: 0
      add :active_users, :bigint, null: false, default: 0
      add :effective_active_users, :bigint, null: false, default: 0
      add :active_mobile_bindings, :bigint, null: false, default: 0

      # 充值相关指标
      add :recharge_users, :bigint, null: false, default: 0
      add :new_recharge_users, :bigint, null: false, default: 0
      add :first_pay_users, :bigint, null: false, default: 0
      add :recharge_amount, :decimal, null: false, default: 0
      add :withdrawal_amount, :decimal, null: false, default: 0
      add :withdrawal_users, :bigint, null: false, default: 0

      # 比率相关指标
      add :withdrawal_recharge_ratio, :decimal, null: false, default: 0
      add :payment_rate, :decimal, null: false, default: 0
      add :new_payment_rate, :decimal, null: false, default: 0
      add :effective_payment_rate, :decimal, null: false, default: 0
      add :effective_new_payment_rate, :decimal, null: false, default: 0

      # ARPU相关指标
      add :arpu, :decimal, null: false, default: 0
      add :arppu, :decimal, null: false, default: 0
      add :effective_arpu, :decimal, null: false, default: 0

      # 留存相关指标
      add :retention_day2, :decimal, null: false, default: 0
      add :retention_day3, :decimal, null: false, default: 0
      add :retention_day7, :decimal, null: false, default: 0
      add :retention_day14, :decimal, null: false, default: 0
      add :retention_day30, :decimal, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    # 创建月报表
    create table(:monthly_reports, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :report_month, :text, null: false
      add :platform_id, :uuid, null: true

      # 月度汇总指标
      add :total_new_devices, :bigint, null: false, default: 0
      add :total_new_registrations, :bigint, null: false, default: 0
      add :total_effective_new_users, :bigint, null: false, default: 0
      add :total_mobile_registrations, :bigint, null: false, default: 0
      add :total_active_users, :bigint, null: false, default: 0
      add :total_effective_active_users, :bigint, null: false, default: 0
      add :total_recharge_users, :bigint, null: false, default: 0
      add :total_recharge_amount, :decimal, null: false, default: 0
      add :total_withdrawal_amount, :decimal, null: false, default: 0

      # 月度平均指标
      add :avg_payment_rate, :decimal, null: false, default: 0
      add :avg_arpu, :decimal, null: false, default: 0
      add :avg_arppu, :decimal, null: false, default: 0
      add :avg_retention_day2, :decimal, null: false, default: 0
      add :avg_retention_day7, :decimal, null: false, default: 0
      add :avg_retention_day30, :decimal, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    # 创建实时统计表
    create table(:realtime_stats, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :stat_time, :utc_datetime_usec, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
      add :platform_id, :uuid, null: true

      # 在线统计
      add :online_users, :bigint, null: false, default: 0
      add :active_games, :bigint, null: false, default: 0
      add :active_rooms, :bigint, null: false, default: 0

      # 实时充值提现
      add :realtime_recharge, :decimal, null: false, default: 0
      add :realtime_withdrawal, :decimal, null: false, default: 0

      # 今日统计
      add :today_new_users, :bigint, null: false, default: 0
      add :today_active_users, :bigint, null: false, default: 0
      add :today_recharge_users, :bigint, null: false, default: 0
      add :today_recharge_amount, :decimal, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    # 创建索引
    create unique_index(:daily_reports, [:report_date, :platform_id],
             name: "daily_reports_unique_date_platform_index"
           )

    create unique_index(:monthly_reports, [:report_month, :platform_id],
             name: "monthly_reports_unique_month_platform_index"
           )

    create index(:daily_reports, [:report_date])
    create index(:daily_reports, [:platform_id])
    create index(:monthly_reports, [:report_month])
    create index(:monthly_reports, [:platform_id])
    create index(:realtime_stats, [:stat_time])
    create index(:realtime_stats, [:platform_id])
  end

  def down do
    drop_if_exists index(:realtime_stats, [:platform_id])
    drop_if_exists index(:realtime_stats, [:stat_time])
    drop_if_exists index(:monthly_reports, [:platform_id])
    drop_if_exists index(:monthly_reports, [:report_month])
    drop_if_exists index(:daily_reports, [:platform_id])
    drop_if_exists index(:daily_reports, [:report_date])

    drop_if_exists unique_index(:monthly_reports, [:report_month, :platform_id],
                     name: "monthly_reports_unique_month_platform_index"
                   )

    drop_if_exists unique_index(:daily_reports, [:report_date, :platform_id],
                     name: "daily_reports_unique_date_platform_index"
                   )

    drop table(:realtime_stats)
    drop table(:monthly_reports)
    drop table(:daily_reports)
  end
end
