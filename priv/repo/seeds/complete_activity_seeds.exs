# 完整活动系统种子数据
# 运行方式: mix run priv/repo/seeds/complete_activity_seeds.exs

import Ash.Query

alias Teen.ActivitySystem.{
  SignInActivity,
  CdkeyActivity,
  GameTask,
  WeeklyCard,
  SevenDayTask,
  VipGift,
  RechargeTask,
  RechargeWheel,
  WheelPrizeConfig,
  ScratchCardActivity,
  ScratchCardTaskLevel,
  ScratchCardLevelReward,
  FirstRechargeGift,
  LossRebateJar,
  InviteCashActivity,
  InviteRewardConfig,
  BindingReward,
  FreeBonusTask,
  LoginCashActivity,
  BrokeAwardActivity,
  FreeCashActivity
}

IO.puts("🎯 开始创建完整活动系统种子数据...")

# 辅助函数：安全创建记录
safe_create = fn module, attrs, unique_field ->
  unique_value = Map.get(attrs, unique_field)

  case apply(module, :read, []) do
    {:ok, records} ->
      existing = Enum.find(records, fn r ->
        Map.get(r, unique_field) == unique_value
      end)

      if existing do
        {:exists, existing}
      else
        apply(module, :create, [attrs])
      end
    _ ->
      apply(module, :create, [attrs])
  end
end

# ==================== 1. 签到活动 ====================
IO.puts("📅 创建签到活动...")

signin_activities = [
  %{
    activity_name: "每日签到",
    description: "每日签到获得奖励，连续签到奖励更丰厚",
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day),
    status: 1,  # 1-启用，0-禁用
    daily_rewards: [
      %{"day" => 1, "coins" => 100, "items" => []},
      %{"day" => 2, "coins" => 200, "items" => []},
      %{"day" => 3, "coins" => 300, "items" => []},
      %{"day" => 4, "coins" => 400, "items" => []},
      %{"day" => 5, "coins" => 500, "items" => []},
      %{"day" => 6, "coins" => 600, "items" => []},
      %{"day" => 7, "coins" => 1000, "items" => ["lucky_card"]}
    ],
    consecutive_rewards: [
      %{"days" => 7, "coins" => 500, "items" => []},
      %{"days" => 14, "coins" => 1000, "items" => []},
      %{"days" => 30, "coins" => 3000, "items" => ["monthly_bonus"]}
    ],
    monthly_rewards: [
      %{"day" => 1, "coins" => 500, "items" => []},
      %{"day" => 15, "coins" => 2000, "items" => []},
      %{"day" => 30, "coins" => 5000, "items" => ["monthly_grand_prize"]}
    ],
    vip_bonus_rate: Decimal.new("20"),  # VIP奖励加成20%
    max_consecutive_days: 30
  },
  %{
    activity_name: "周末签到",
    description: "周末特别签到活动",
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day),
    status: 1,
    daily_rewards: [
      %{"day" => 1, "coins" => 200, "items" => []},
      %{"day" => 2, "coins" => 500, "items" => ["weekend_bonus"]}
    ],
    consecutive_rewards: [],
    monthly_rewards: [],
    vip_bonus_rate: Decimal.new("30"),
    max_consecutive_days: 7
  }
]

Enum.each(signin_activities, fn activity_data ->
  case SignInActivity
       |> Ash.Query.filter(activity_name == ^activity_data.activity_name)
       |> Ash.read_one() do
    {:ok, activity} when not is_nil(activity) ->
      IO.puts("ℹ️ 签到活动已存在: #{activity.activity_name}")
    {:ok, nil} ->
      case Ash.create(SignInActivity, activity_data) do
        {:ok, activity} -> IO.puts("✅ 签到活动创建成功: #{activity.activity_name}")
        {:error, reason} -> IO.puts("❌ 签到活动创建失败: #{inspect(reason)}")
      end
    {:error, reason} ->
      IO.puts("❌ 签到活动查询失败: #{inspect(reason)}")
  end
end)

# ==================== 3. 游戏任务 ====================
IO.puts("🎮 创建游戏任务...")

game_tasks = [
  %{
    task_name: "Teen Patti游戏任务",
    game_id: 1,  # Teen Patti 游戏ID
    task_type: :game_rounds,
    required_count: 10,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 500, description: "游戏任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "Teen Patti胜利任务",
    game_id: 1,  # Teen Patti 游戏ID
    task_type: :win_rounds,
    required_count: 5,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 1000, description: "胜利任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "Dragon Tiger游戏任务",
    game_id: 22,  # Dragon Tiger 游戏ID
    task_type: :game_rounds,
    required_count: 15,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 300, description: "游戏任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "Dragon Tiger胜利任务",
    game_id: 22,  # Dragon Tiger 游戏ID
    task_type: :win_rounds,
    required_count: 8,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 800, description: "胜利任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "Slot777游戏任务",
    game_id: 40,  # Slot777 游戏ID
    task_type: :game_rounds,
    required_count: 20,
    max_claims: 2,
    rewards: [
      %{type: :coins, amount: 200, description: "游戏任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "Slot777胜利任务",
    game_id: 40,  # Slot777 游戏ID
    task_type: :win_rounds,
    required_count: 10,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 600, description: "胜利任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "SlotCat游戏任务",
    game_id: 42,  # SlotCat 游戏ID
    task_type: :game_rounds,
    required_count: 10,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 1000, description: "游戏任务奖励"}
    ],
    status: :enabled
  },
  %{
    task_name: "SlotCat胜利任务",
    game_id: 42,  # SlotCat 游戏ID
    task_type: :win_rounds,
    required_count: 5,
    max_claims: 1,
    rewards: [
      %{type: :coins, amount: 2000, description: "胜利任务奖励"}
    ],
    status: :enabled
  }
]

# 特殊处理 GameTask 的唯一性验证（基于 game_id 和 task_type 组合）
safe_create_game_task = fn attrs ->
  game_id = Map.get(attrs, :game_id)
  task_type = Map.get(attrs, :task_type)

  case GameTask.read() do
    {:ok, records} ->
      existing = Enum.find(records, fn r ->
        r.game_id == game_id and r.task_type == task_type
      end)

      if existing do
        {:exists, existing}
      else
        # 禁用唯一性验证，因为我们已经手动检查了
        attrs_with_validation = Map.put(attrs, :validate_create_uniqueness, false)
        GameTask.create(attrs_with_validation)
      end
    _ ->
      attrs_with_validation = Map.put(attrs, :validate_create_uniqueness, false)
      GameTask.create(attrs_with_validation)
  end
end

Enum.each(game_tasks, fn task_data ->
  case safe_create_game_task.(task_data) do
    {:ok, task} -> IO.puts("✅ 游戏任务创建成功: #{task.task_name}")
    {:exists, task} -> IO.puts("ℹ️ 游戏任务已存在: #{task.task_name}")
    {:error, reason} -> IO.puts("❌ 游戏任务创建失败: #{inspect(reason)}")
  end
end)

# ==================== 4. 周卡活动 ====================
IO.puts("📋 创建周卡活动...")

weekly_cards = [
  %{
    title: "标准周卡",
    recharge_amount: Decimal.new("999"),  # 充值金额（分）
    initial_rewards: [
      %{type: :coins, amount: 100, description: "购买即得奖励"}
    ],
    daily_rewards: [
      %{type: :coins, amount: 200, description: "每日签到奖励"}
    ],
    claim_days: 7,                        # 可领取天数
    status: :enabled
  },
  %{
    title: "豪华周卡",
    recharge_amount: Decimal.new("1999"),
    initial_rewards: [
      %{type: :coins, amount: 300, description: "购买即得奖励"}
    ],
    daily_rewards: [
      %{type: :coins, amount: 500, description: "每日签到奖励"}
    ],
    claim_days: 7,
    status: :enabled
  },
  %{
    title: "至尊周卡",
    recharge_amount: Decimal.new("4999"),
    initial_rewards: [
      %{type: :coins, amount: 800, description: "购买即得奖励"}
    ],
    daily_rewards: [
      %{type: :coins, amount: 1000, description: "每日签到奖励"}
    ],
    claim_days: 7,
    status: :enabled
  }
]

Enum.each(weekly_cards, fn card_data ->
  case safe_create.(WeeklyCard, card_data, :title) do
    {:ok, card} -> IO.puts("✅ 周卡创建成功: #{card.title}")
    {:exists, card} -> IO.puts("ℹ️ 周卡已存在: #{card.title}")
    {:error, reason} -> IO.puts("❌ 周卡创建失败: #{inspect(reason)}")
  end
end)

# ==================== 5. 七日任务 ====================

IO.puts("🎯 开始创建七日签到任务配置...")

# 定义七日任务配置
seven_day_tasks = [
  %{
    day_number: 1,
    description: "连续登录第1天奖励",
    rewards: [
      %{type: :coins, amount: 100, description: "登录奖励金币"},
      %{type: :experience, amount: 10, description: "经验积分"}
    ],
    is_cyclic: true,
    is_special: false,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 2,
    description: "连续登录第2天奖励",
    rewards: [
      %{type: :coins, amount: 200, description: "登录奖励金币"},
      %{type: :experience, amount: 20, description: "经验积分"}
    ],
    is_cyclic: true,
    is_special: false,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 3,
    description: "连续登录第3天奖励",
    rewards: [
      %{type: :coins, amount: 300, description: "登录奖励金币"},
      %{type: :experience, amount: 30, description: "经验积分"},
      %{type: :items, amount: 1, description: "幸运道具"}
    ],
    is_cyclic: true,
    is_special: true,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 4,
    description: "连续登录第4天奖励",
    rewards: [
      %{type: :coins, amount: 400, description: "登录奖励金币"},
      %{type: :experience, amount: 40, description: "经验积分"}
    ],
    is_cyclic: true,
    is_special: false,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 5,
    description: "连续登录第5天奖励",
    rewards: [
      %{type: :coins, amount: 500, description: "登录奖励金币"},
      %{type: :experience, amount: 50, description: "经验积分"},
      %{type: :items, amount: 2, description: "幸运道具"}
    ],
    is_cyclic: true,
    is_special: true,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 6,
    description: "连续登录第6天奖励",
    rewards: [
      %{type: :coins, amount: 600, description: "登录奖励金币"},
      %{type: :experience, amount: 60, description: "经验积分"}
    ],
    is_cyclic: true,
    is_special: false,
    status: :enabled,
    validate_uniqueness: false
  },
  %{
    day_number: 7,
    description: "连续登录第7天奖励 - 大奖！",
    rewards: [
      %{type: :coins, amount: 1000, description: "登录大奖金币"},
      %{type: :experience, amount: 100, description: "经验积分"},
      %{type: :items, amount: 5, description: "豪华礼包"}
    ],
    is_cyclic: true,
    is_special: true,
    status: :enabled,
    validate_uniqueness: false
  }
]

# 创建七日任务
Enum.each(seven_day_tasks, fn task_config ->
  case SevenDayTask.create(task_config) do
    {:ok, task} ->
      IO.puts("✅ 创建第#{task.day_number}天任务成功: #{task.description}")

      # 显示奖励详情
      Enum.each(task.rewards, fn reward ->
        reward_type = case reward.type do
          :coins -> "金币"
          :experience -> "经验"
          :items -> "道具"
          _ -> "未知"
        end
        IO.puts("   • #{reward_type}: #{reward.amount} - #{reward.description}")
      end)

    {:exists, task} ->
      IO.puts("✅ 创建第#{task.day_number}天任务已存在: #{task.description}")
    {:error, reason} ->
      IO.puts("❌ 创建第#{task_config.day_number}天任务失败: #{inspect(reason)}")
  end
end)

IO.puts("\n🎯 七日签到任务配置完成！")

# 验证创建结果
case SevenDayTask.list_active_tasks() do
  {:ok, tasks} ->
    IO.puts("\n📋 当前活跃的七日任务:")
    Enum.each(tasks, fn task ->
      special_mark = if task.is_special, do: " ⭐", else: ""
      IO.puts("  • 第#{task.day_number}天: #{task.description}#{special_mark}")
    end)
    IO.puts("\n✅ 总计 #{length(tasks)} 个七日任务已激活")

  {:error, reason} ->
    IO.puts("❌ 查询七日任务失败: #{inspect(reason)}")
end

# ==================== 6. 刮刮卡活动 ====================
IO.puts("🎫 创建刮刮卡活动...")

scratch_card_activities = [
  %{
    activity_title: "新手刮刮卡",
    card_type: :bronze,
    cost_amount: Decimal.new("100"),  # 100金币
    rewards: [
      %{type: :coins, amount: 50, description: "安慰奖", probability: 40},
      %{type: :coins, amount: 200, description: "小奖", probability: 30},
      %{type: :coins, amount: 500, description: "中奖", probability: 20},
      %{type: :coins, amount: 1000, description: "大奖", probability: 9},
      %{type: :coins, amount: 5000, description: "超级大奖", probability: 1}
    ],
    claimable_count: 30,
    reward_probability: Decimal.new("80"),  # 80%中奖率
    daily_limit: 10,
    is_active: true,
    status: :enabled
  },
  %{
    activity_title: "白银刮刮卡",
    card_type: :silver,
    cost_amount: Decimal.new("500"),  # 500金币
    rewards: [
      %{type: :coins, amount: 300, description: "安慰奖", probability: 35},
      %{type: :coins, amount: 800, description: "小奖", probability: 30},
      %{type: :coins, amount: 1500, description: "中奖", probability: 20},
      %{type: :coins, amount: 3000, description: "大奖", probability: 12},
      %{type: :coins, amount: 10000, description: "超级大奖", probability: 3}
    ],
    claimable_count: 30,
    reward_probability: Decimal.new("85"),  # 85%中奖率
    daily_limit: 5,
    is_active: true,
    status: :enabled
  },
  %{
    activity_title: "黄金刮刮卡",
    card_type: :gold,
    cost_amount: Decimal.new("1000"),  # 1000金币
    rewards: [
      %{type: :coins, amount: 600, description: "安慰奖", probability: 30},
      %{type: :coins, amount: 1500, description: "小奖", probability: 30},
      %{type: :coins, amount: 3000, description: "中奖", probability: 25},
      %{type: :coins, amount: 6000, description: "大奖", probability: 12},
      %{type: :coins, amount: 20000, description: "超级大奖", probability: 3}
    ],
    claimable_count: 30,
    reward_probability: Decimal.new("90"),  # 90%中奖率
    daily_limit: 3,
    is_active: true,
    status: :enabled
  }
]

Enum.each(scratch_card_activities, fn activity_data ->
  case safe_create.(ScratchCardActivity, activity_data, :activity_title) do
    {:ok, activity} -> IO.puts("✅ 刮刮卡活动创建成功: #{activity.activity_title}")
    {:exists, activity} -> IO.puts("ℹ️ 刮刮卡活动已存在: #{activity.activity_title}")
    {:error, reason} -> IO.puts("❌ 刮刮卡活动创建失败: #{inspect(reason)}")
  end
end)

# ==================== 6. VIP礼包 ====================
IO.puts("👑 创建VIP礼包...")

vip_gifts = [
  %{
    vip_level: 1,
    daily_rewards: [
      %{type: :coins, amount: 100, description: "VIP1每日奖励"}
    ],
    weekly_rewards: [
      %{type: :coins, amount: 500, description: "VIP1每周奖励"}
    ],
    monthly_rewards: [
      %{type: :coins, amount: 2000, description: "VIP1每月奖励"}
    ],
    status: :enabled
  },
  %{
    vip_level: 2,
    daily_rewards: [
      %{type: :coins, amount: 200, description: "VIP2每日奖励"}
    ],
    weekly_rewards: [
      %{type: :coins, amount: 1000, description: "VIP2每周奖励"}
    ],
    monthly_rewards: [
      %{type: :coins, amount: 4000, description: "VIP2每月奖励"}
    ],
    status: :enabled
  },
  %{
    vip_level: 3,
    daily_rewards: [
      %{type: :coins, amount: 300, description: "VIP3每日奖励"}
    ],
    weekly_rewards: [
      %{type: :coins, amount: 1500, description: "VIP3每周奖励"}
    ],
    monthly_rewards: [
      %{type: :coins, amount: 6000, description: "VIP3每月奖励"}
    ],
    status: :enabled
  },
  %{
    vip_level: 4,
    daily_rewards: [
      %{type: :coins, amount: 500, description: "VIP4每日奖励"}
    ],
    weekly_rewards: [
      %{type: :coins, amount: 2500, description: "VIP4每周奖励"}
    ],
    monthly_rewards: [
      %{type: :coins, amount: 10000, description: "VIP4每月奖励"}
    ],
    status: :enabled
  },
  %{
    vip_level: 5,
    daily_rewards: [
      %{type: :coins, amount: 800, description: "VIP5每日奖励"}
    ],
    weekly_rewards: [
      %{type: :coins, amount: 4000, description: "VIP5每周奖励"}
    ],
    monthly_rewards: [
      %{type: :coins, amount: 16000, description: "VIP5每月奖励"}
    ],
    status: :enabled
  }
]

Enum.each(vip_gifts, fn gift_data ->
  case safe_create.(VipGift, gift_data, :vip_level) do
    {:ok, gift} -> IO.puts("✅ VIP礼包创建成功: VIP#{gift.vip_level}")
    {:exists, gift} -> IO.puts("ℹ️ VIP礼包已存在: VIP#{gift.vip_level}")
    {:error, reason} -> IO.puts("❌ VIP礼包创建失败: #{inspect(reason)}")
  end
end)

# ==================== 7. 充值任务 ====================
IO.puts("💳 创建充值任务...")

recharge_tasks = [
  %{
    recharge_amount: Decimal.new("1000"),  # 充值10元
    reward_amount: Decimal.new("500"),     # 奖励5元
    status: :enabled
  },
  %{
    recharge_amount: Decimal.new("2000"),  # 充值20元
    reward_amount: Decimal.new("1200"),    # 奖励12元
    status: :enabled
  },
  %{
    recharge_amount: Decimal.new("5000"),  # 充值50元
    reward_amount: Decimal.new("3500"),    # 奖励35元
    status: :enabled
  },
  %{
    recharge_amount: Decimal.new("10000"), # 充值100元
    reward_amount: Decimal.new("8000"),    # 奖励80元
    status: :enabled
  },
  %{
    recharge_amount: Decimal.new("20000"), # 充值200元
    reward_amount: Decimal.new("18000"),   # 奖励180元
    status: :enabled
  }
]

Enum.each(recharge_tasks, fn task_data ->
  case safe_create.(RechargeTask, task_data, :recharge_amount) do
    {:ok, task} -> IO.puts("✅ 充值任务创建成功: 充值#{Decimal.to_integer(Decimal.div(task.recharge_amount, 100))}元")
    {:exists, task} -> IO.puts("ℹ️ 充值任务已存在: 充值#{Decimal.to_integer(Decimal.div(task.recharge_amount, 100))}元")
    {:error, reason} -> IO.puts("❌ 充值任务创建失败: #{inspect(reason)}")
  end
end)

# ==================== 8. 充值转盘 ====================
IO.puts("🎰 创建充值转盘...")

recharge_wheels = [
  %{
    cumulative_recharge: Decimal.new("1000"),  # 累计充值10元
    wheel_spins: 1,                            # 获得1次转盘机会
    status: :enabled
  },
  %{
    cumulative_recharge: Decimal.new("5000"),  # 累计充值50元
    wheel_spins: 3,                            # 获得3次转盘机会
    status: :enabled
  },
  %{
    cumulative_recharge: Decimal.new("10000"), # 累计充值100元
    wheel_spins: 5,                            # 获得5次转盘机会
    status: :enabled
  },
  %{
    cumulative_recharge: Decimal.new("20000"), # 累计充值200元
    wheel_spins: 10,                           # 获得10次转盘机会
    status: :enabled
  }
]

Enum.each(recharge_wheels, fn wheel_data ->
  case safe_create.(RechargeWheel, wheel_data, :cumulative_recharge) do
    {:ok, wheel} -> IO.puts("✅ 充值转盘配置创建成功: 充值#{Decimal.to_integer(Decimal.div(wheel.cumulative_recharge, 100))}元获得#{wheel.wheel_spins}次机会")
    {:exists, wheel} -> IO.puts("ℹ️ 充值转盘配置已存在: 充值#{Decimal.to_integer(Decimal.div(wheel.cumulative_recharge, 100))}元")
    {:error, reason} -> IO.puts("❌ 充值转盘配置创建失败: #{inspect(reason)}")
  end
end)

# 创建转盘奖品配置
IO.puts("🎁 创建转盘奖品配置...")

wheel_prizes = [
  %{
    prize_type: :coins,
    prize_value: Decimal.new("100"),
    probability: Decimal.new("30"),
    sort_order: 1
  },
  %{
    prize_type: :coins,
    prize_value: Decimal.new("200"),
    probability: Decimal.new("25"),
    sort_order: 2
  },
  %{
    prize_type: :coins,
    prize_value: Decimal.new("500"),
    probability: Decimal.new("20"),
    sort_order: 3
  },
  %{
    prize_type: :coins,
    prize_value: Decimal.new("1000"),
    probability: Decimal.new("15"),
    sort_order: 4
  },
  %{
    prize_type: :coins,
    prize_value: Decimal.new("2000"),
    probability: Decimal.new("8"),
    sort_order: 5
  },
  %{
    prize_type: :coins,
    prize_value: Decimal.new("5000"),
    probability: Decimal.new("2"),
    sort_order: 6
  }
]

Enum.each(wheel_prizes, fn prize_data ->
  case WheelPrizeConfig.create(prize_data) do
    {:ok, prize} -> IO.puts("  ✅ 转盘奖品创建成功: #{Decimal.to_integer(prize.prize_value)}金币 (#{Decimal.to_string(prize.probability)}%)")
    {:error, reason} -> IO.puts("  ❌ 转盘奖品创建失败: #{inspect(reason)}")
  end
end)

# ==================== 9. 首充礼包 ====================
IO.puts("🎁 创建首充礼包...")

first_recharge_gifts = [
  %{
    title: "新手首充礼包",
    limit_days: 7,                        # 注册7天内有效
    min_recharge_amount: Decimal.new("1000"),  # 最小充值金额（分）
    rewards: [
      %{type: :coins, amount: 1500, description: "首充奖励金币"}
    ],
    bonus_multiplier: Decimal.new("1.5"), # 奖励倍数
    time_limit_hours: 168,                # 时间限制（7天=168小时）
    description: "新用户专享首充礼包，充值即可获得150%奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "限时首充礼包",
    limit_days: 3,                        # 注册3天内有效
    min_recharge_amount: Decimal.new("2000"),  # 最小充值金额（分）
    rewards: [
      %{type: :coins, amount: 4000, description: "首充奖励金币"}
    ],
    bonus_multiplier: Decimal.new("2.0"), # 奖励倍数
    time_limit_hours: 72,                 # 时间限制（3天=72小时）
    description: "限时特惠首充礼包，充值即可获得200%奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "超值首充礼包",
    limit_days: 1,                        # 注册1天内有效
    min_recharge_amount: Decimal.new("5000"),  # 最小充值金额（分）
    rewards: [
      %{type: :coins, amount: 15000, description: "首充奖励金币"}
    ],
    bonus_multiplier: Decimal.new("3.0"), # 奖励倍数
    time_limit_hours: 24,                 # 时间限制（1天=24小时）
    description: "超值首充礼包，充值即可获得300%奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "VIP首充礼包",
    limit_days: 7,                        # 注册7天内有效
    min_recharge_amount: Decimal.new("10000"), # 最小充值金额（分）
    rewards: [
      %{type: :coins, amount: 25000, description: "首充奖励金币"}
    ],
    bonus_multiplier: Decimal.new("2.5"), # 奖励倍数
    time_limit_hours: 168,                # 时间限制（7天=168小时）
    description: "VIP专享首充礼包，高额充值获得更多奖励",
    is_active: true,
    status: :enabled
  }
]

Enum.each(first_recharge_gifts, fn gift_data ->
  case safe_create.(FirstRechargeGift, gift_data, :title) do
    {:ok, gift} -> IO.puts("✅ 首充礼包创建成功: #{gift.title}")
    {:exists, gift} -> IO.puts("ℹ️ 首充礼包已存在: #{gift.title}")
    {:error, reason} -> IO.puts("❌ 首充礼包创建失败: #{inspect(reason)}")
  end
end)

# ==================== 10. 亏损返利罐 ====================
IO.puts("🏺 创建亏损返利罐...")

loss_rebate_jars = [
  %{
    title: "新手保护罐",
    max_claims: 1,                        # 每日最多领取1次
    loss_threshold: Decimal.new("1000"),  # 亏损阈值（分）
    rebate_percentage: Decimal.new("10"), # 返利百分比
    max_rebate: Decimal.new("500"),       # 最大返利（分）
    calculation_period: :daily,           # 计算周期
    rebate_type: :coins,                  # 返利类型
    auto_distribute: false,               # 自动发放
    is_active: true,                      # 是否激活
    status: :enabled
  },
  %{
    title: "标准返利罐",
    max_claims: 1,
    loss_threshold: Decimal.new("2000"),
    rebate_percentage: Decimal.new("12"),
    max_rebate: Decimal.new("1000"),
    calculation_period: :daily,
    rebate_type: :coins,
    auto_distribute: false,
    is_active: true,
    status: :enabled
  },
  %{
    title: "高级返利罐",
    max_claims: 2,
    loss_threshold: Decimal.new("5000"),
    rebate_percentage: Decimal.new("15"),
    max_rebate: Decimal.new("2000"),
    calculation_period: :daily,
    rebate_type: :coins,
    auto_distribute: true,                # 高级返利罐自动发放
    is_active: true,
    status: :enabled
  },
  %{
    title: "VIP返利罐",
    max_claims: 3,
    loss_threshold: Decimal.new("10000"),
    rebate_percentage: Decimal.new("20"),
    max_rebate: Decimal.new("5000"),
    calculation_period: :weekly,          # VIP返利罐按周计算
    rebate_type: :coins,
    auto_distribute: true,                # VIP返利罐自动发放
    is_active: true,
    status: :enabled
  }
]

Enum.each(loss_rebate_jars, fn jar_data ->
  case safe_create.(LossRebateJar, jar_data, :title) do
    {:ok, jar} -> IO.puts("✅ 亏损返利罐创建成功: #{jar.title}")
    {:exists, jar} -> IO.puts("ℹ️ 亏损返利罐已存在: #{jar.title}")
    {:error, reason} -> IO.puts("❌ 亏损返利罐创建失败: #{inspect(reason)}")
  end
end)

# ==================== 11. 邀请现金活动 ====================
IO.puts("👥 创建邀请现金活动...")

invite_activities = [
  %{
    title: "邀请好友赚现金",
    total_reward: Decimal.new("10000"),   # 总奖励金额（分）
    initial_min: Decimal.new("500"),      # 初始最小值（分）
    initial_max: Decimal.new("1000"),     # 初始最大值（分）
    status: :enabled
  },
  %{
    title: "超级邀请活动",
    total_reward: Decimal.new("20000"),
    initial_min: Decimal.new("1000"),
    initial_max: Decimal.new("2000"),
    status: :enabled
  }
]

created_activities = []
Enum.each(invite_activities, fn activity_data ->
  case safe_create.(InviteCashActivity, activity_data, :title) do
    {:ok, activity} ->
      IO.puts("✅ 邀请现金活动创建成功: #{activity.title}")
      created_activities = [activity | created_activities]
    {:exists, activity} ->
      IO.puts("ℹ️ 邀请现金活动已存在: #{activity.title}")
      created_activities = [activity | created_activities]
    {:error, reason} -> IO.puts("❌ 邀请现金活动创建失败: #{inspect(reason)}")
  end
end)

# 创建邀请奖励配置
if length(created_activities) > 0 do
  activity = hd(created_activities)

  invite_rewards = [
    %{
      activity_id: activity.id,
      round_number: 1,
      reward_type: :invite_new_friend,
      min_reward: Decimal.new("500"),
      max_reward: Decimal.new("1000")
    },
    %{
      activity_id: activity.id,
      round_number: 2,
      reward_type: :invite_new_friend,
      min_reward: Decimal.new("800"),
      max_reward: Decimal.new("1500")
    },
    %{
      activity_id: activity.id,
      round_number: 3,
      reward_type: :invite_recharge,
      min_reward: Decimal.new("1000"),
      max_reward: Decimal.new("2000")
    }
  ]

  Enum.each(invite_rewards, fn reward_data ->
    case InviteRewardConfig.create(reward_data) do
      {:ok, reward} -> IO.puts("  ✅ 邀请奖励配置创建成功: 第#{reward.round_number}轮")
      {:error, reason} -> IO.puts("  ❌ 邀请奖励配置创建失败: #{inspect(reason)}")
    end
  end)
end

# ==================== 12. 绑定奖励 ====================
IO.puts("📱 创建绑定奖励...")

binding_rewards = [
  %{
    title: "手机绑定奖励",
    binding_type: :phone,
    rewards: [
      %{type: :coins, amount: 500, description: "手机绑定奖励"}
    ],
    one_time_only: true,                  # 仅限一次
    verification_required: true,          # 需要验证
    description: "绑定手机号码即可获得500金币奖励",
    is_active: true,                      # 是否激活
    status: :enabled
  },
  %{
    title: "邮箱绑定奖励",
    binding_type: :email,
    rewards: [
      %{type: :coins, amount: 300, description: "邮箱绑定奖励"}
    ],
    one_time_only: true,                  # 仅限一次
    verification_required: true,          # 需要验证
    description: "绑定邮箱地址即可获得300金币奖励",
    is_active: true,                      # 是否激活
    status: :enabled
  },
  %{
    title: "银行卡绑定奖励",
    binding_type: :bank_card,
    rewards: [
      %{type: :coins, amount: 1000, description: "银行卡绑定奖励"}
    ],
    one_time_only: true,                  # 仅限一次
    verification_required: true,          # 需要验证
    description: "绑定银行卡即可获得1000金币奖励",
    is_active: true,                      # 是否激活
    status: :enabled
  },
  %{
    title: "身份证绑定奖励",
    binding_type: :id_card,
    rewards: [
      %{type: :coins, amount: 800, description: "身份证绑定奖励"}
    ],
    one_time_only: true,                  # 仅限一次
    verification_required: true,          # 需要验证
    description: "绑定身份证即可获得800金币奖励",
    is_active: true,                      # 是否激活
    status: :enabled
  }
]

Enum.each(binding_rewards, fn reward_data ->
  case safe_create.(BindingReward, reward_data, :binding_type) do
    {:ok, reward} -> IO.puts("✅ 绑定奖励创建成功: #{reward.title}")
    {:exists, reward} -> IO.puts("ℹ️ 绑定奖励已存在: #{reward.title}")
    {:error, reason} -> IO.puts("❌ 绑定奖励创建失败: #{inspect(reason)}")
  end
end)

# ==================== 13. 免费积分任务 ====================
IO.puts("🆓 创建免费积分任务...")

free_bonus_tasks = [
  %{
    title: "每日分享任务",
    share_count: 1,
    game_id: nil,
    required_win_coins: Decimal.new("0"),
    withdraw_count: 1,
    rewards: [
      %{type: :coins, amount: 100, description: "分享任务奖励"}
    ],
    status: :enabled
  },
  %{
    title: "游戏体验任务",
    share_count: 1,
    game_id: "teen_patti",
    required_win_coins: Decimal.new("500"),
    withdraw_count: 1,
    rewards: [
      %{type: :coins, amount: 200, description: "游戏体验奖励"}
    ],
    status: :enabled
  },
  %{
    title: "提现激励任务",
    share_count: 1,
    game_id: nil,
    required_win_coins: Decimal.new("0"),
    withdraw_count: 1,
    rewards: [
      %{type: :coins, amount: 300, description: "提现激励奖励"}
    ],
    status: :enabled
  },
  %{
    title: "Dragon Tiger体验任务",
    share_count: 1,
    game_id: "dragon_tiger",
    required_win_coins: Decimal.new("1000"),
    withdraw_count: 1,
    rewards: [
      %{type: :coins, amount: 250, description: "Dragon Tiger体验奖励"}
    ],
    status: :enabled
  },
  %{
    title: "综合任务",
    share_count: 2,
    game_id: "slot777",
    required_win_coins: Decimal.new("800"),
    withdraw_count: 1,
    rewards: [
      %{type: :coins, amount: 500, description: "综合任务奖励"}
    ],
    status: :enabled
  }
]

Enum.each(free_bonus_tasks, fn task_data ->
  case safe_create.(FreeBonusTask, task_data, :title) do
    {:ok, task} -> IO.puts("✅ 免费积分任务创建成功: #{task.title}")
    {:exists, task} -> IO.puts("ℹ️ 免费积分任务已存在: #{task.title}")
    {:error, reason} -> IO.puts("❌ 免费积分任务创建失败: #{inspect(reason)}")
  end
end)

# ==================== 14. 登录现金活动 ====================
IO.puts("💰 创建登录现金活动...")

login_cash_activities = [
  %{
    activity_name: "每日登录现金",
    description: "每日登录获得现金，完成任务获得更多奖励",
    reward_type: :cash,
    total_reward: Decimal.new("5000"),
    task_config: %{
      "login_target" => 1,
      "recharge_target" => 1000,
      "game_target" => 10,
      "wheel_target" => 3,
      "win_target" => 1000
    },
    reward_config: %{
      "login_reward" => 50,
      "recharge_reward" => 200,
      "game_reward" => 100,
      "wheel_reward" => 150,
      "win_reward" => 300,
      "complete_all_reward" => 500
    },
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day),
    status: :active
  },
  %{
    activity_name: "周末登录现金",
    description: "周末特别登录现金活动",
    reward_type: :cash,
    total_reward: Decimal.new("3000"),
    task_config: %{
      "login_target" => 2,
      "game_target" => 5,
      "win_target" => 500
    },
    reward_config: %{
      "login_reward" => 100,
      "game_reward" => 200,
      "win_reward" => 300
    },
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 30, :day),
    status: :active
  }
]

Enum.each(login_cash_activities, fn activity_data ->
  case LoginCashActivity
       |> Ash.Query.filter(activity_name == ^activity_data.activity_name)
       |> Ash.read_one() do
    {:ok, activity} when not is_nil(activity) ->
      IO.puts("ℹ️ 登录现金活动已存在: #{activity.activity_name}")
    {:ok, nil} ->
      case Ash.create(LoginCashActivity, activity_data) do
        {:ok, activity} -> IO.puts("✅ 登录现金活动创建成功: #{activity.activity_name}")
        {:error, reason} -> IO.puts("❌ 登录现金活动创建失败: #{inspect(reason)}")
      end
    {:error, reason} ->
      IO.puts("❌ 登录现金活动查询失败: #{inspect(reason)}")
  end
end)

# ==================== 15. 破产补助活动 ====================
IO.puts("🆘 创建破产补助活动...")

broke_award_activities = [
  %{
    activity_name: "破产救济金",
    description: "余额不足时可申请破产救济金",
    min_balance_threshold: Decimal.new("100"),  # 余额阈值（分）
    award_amount: Decimal.new("500"),           # 补助金额（分）
    max_claims_per_day: 3,                      # 每日最大领取次数
    max_claims_total: 10,                       # 总最大领取次数
    cooldown_hours: 8,                          # 领取间隔时间（小时）
    status: :active,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day)
  },
  %{
    activity_name: "新手破产保护",
    description: "新手专享破产保护，更低门槛更多次数",
    min_balance_threshold: Decimal.new("50"),
    award_amount: Decimal.new("300"),
    max_claims_per_day: 5,
    max_claims_total: 20,
    cooldown_hours: 4,
    status: :active,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day)
  }
]

Enum.each(broke_award_activities, fn activity_data ->
  case BrokeAwardActivity
       |> Ash.Query.filter(activity_name == ^activity_data.activity_name)
       |> Ash.read_one() do
    {:ok, activity} when not is_nil(activity) ->
      IO.puts("ℹ️ 破产补助活动已存在: #{activity.activity_name}")
    {:ok, nil} ->
      case Ash.create(BrokeAwardActivity, activity_data) do
        {:ok, activity} -> IO.puts("✅ 破产补助活动创建成功: #{activity.activity_name}")
        {:error, reason} -> IO.puts("❌ 破产补助活动创建失败: #{inspect(reason)}")
      end
    {:error, reason} ->
      IO.puts("❌ 破产补助活动查询失败: #{inspect(reason)}")
  end
end)

# ==================== 16. 免费现金活动 ====================
IO.puts("🎉 创建免费现金活动...")

free_cash_activities = [
  %{
    activity_name: "新手免费现金",
    description: "新手专享免费现金活动，邀请好友获得更多奖励",
    base_amount: Decimal.new("1000"),      # 基础金额（分）
    invitation_bonus: Decimal.new("200"),  # 邀请奖励（分）
    min_invites: 3,                        # 最少邀请人数
    max_invites: 50,                       # 最大有效邀请人数
    max_claims_per_user: 1,                # 每用户最大领取次数
    invite_requirements: %{
      "min_recharge" => 100,
      "min_play_games" => 5
    },
    status: :active,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 365, :day)
  },
  %{
    activity_name: "周末免费现金",
    description: "周末特别免费现金活动",
    base_amount: Decimal.new("500"),
    invitation_bonus: Decimal.new("100"),
    min_invites: 2,
    max_invites: 30,
    max_claims_per_user: 1,
    invite_requirements: %{
      "min_recharge" => 50,
      "min_play_games" => 3
    },
    status: :active,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 30, :day)
  },
  %{
    activity_name: "节日免费现金",
    description: "节日庆典免费现金活动",
    base_amount: Decimal.new("2000"),
    invitation_bonus: Decimal.new("500"),
    min_invites: 5,
    max_invites: 100,
    max_claims_per_user: 1,
    invite_requirements: %{
      "min_recharge" => 200,
      "min_play_games" => 10
    },
    status: :active,
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 7, :day)
  }
]

Enum.each(free_cash_activities, fn activity_data ->
  case FreeCashActivity
       |> Ash.Query.filter(activity_name == ^activity_data.activity_name)
       |> Ash.read_one() do
    {:ok, activity} when not is_nil(activity) ->
      IO.puts("ℹ️ 免费现金活动已存在: #{activity.activity_name}")
    {:ok, nil} ->
      case Ash.create(FreeCashActivity, activity_data) do
        {:ok, activity} -> IO.puts("✅ 免费现金活动创建成功: #{activity.activity_name}")
        {:error, reason} -> IO.puts("❌ 免费现金活动创建失败: #{inspect(reason)}")
      end
    {:error, reason} ->
      IO.puts("❌ 免费现金活动查询失败: #{inspect(reason)}")
  end
end)

# ==================== 统计信息 ====================
IO.puts("\n📊 活动系统种子数据创建完成！")
IO.puts(String.duplicate("=", 50))

activity_counts = %{
  "签到活动" => length(signin_activities),
  "游戏任务" => length(game_tasks),
  "周卡活动" => length(weekly_cards),
  "七日任务" => length(seven_day_tasks),
  "刮刮卡活动" => length(scratch_card_activities),
  "VIP礼包" => length(vip_gifts),
  "充值任务" => length(recharge_tasks),
  "充值转盘配置" => length(recharge_wheels),
  "转盘奖品配置" => length(wheel_prizes),
  "首充礼包" => length(first_recharge_gifts),
  "亏损返利罐" => length(loss_rebate_jars),
  "邀请现金活动" => length(invite_activities),
  "绑定奖励" => length(binding_rewards),
  "免费积分任务" => length(free_bonus_tasks),
  "登录现金活动" => length(login_cash_activities),
  "破产补助活动" => length(broke_award_activities),
  "免费现金活动" => length(free_cash_activities)
}

Enum.each(activity_counts, fn {name, count} ->
  IO.puts("  • #{name}: #{count} 个")
end)

total_activities = Enum.sum(Map.values(activity_counts))
IO.puts("\n🎯 总计创建活动配置: #{total_activities} 个")
IO.puts("🚀 活动系统已准备就绪，可以开始使用！")
IO.puts("\n💡 运行方式: mix run priv/repo/seeds/complete_activity_seeds.exs")
IO.puts("📝 注意: 如果遇到错误，请检查数据库连接和表结构是否正确")
